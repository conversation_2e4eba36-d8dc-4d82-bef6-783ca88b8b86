{"version": 3, "file": "FormData.d.ts", "sourceRoot": "", "sources": ["../../src/winter/FormData.ts"], "names": [], "mappings": "AAQA,MAAM,MAAM,iBAAiB,GAAG,MAAM,GAAG,IAAI,CAAC;AAC9C,MAAM,MAAM,gBAAgB,GACxB;IAEE,MAAM,EAAE,MAAM,CAAC;IACf,OAAO,EAAE;QAAE,CAAC,IAAI,EAAE,MAAM,GAAG,MAAM,CAAA;KAAE,CAAC;CACrC,GACD;IAEE,IAAI,EAAE,IAAI,CAAC;IACX,OAAO,EAAE;QAAE,CAAC,IAAI,EAAE,MAAM,GAAG,MAAM,CAAA;KAAE,CAAC;IACpC,IAAI,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IAC1B,IAAI,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;CAC3B,GACD;IAEE,GAAG,EAAE,MAAM,CAAC;IACZ,OAAO,EAAE;QAAE,CAAC,IAAI,EAAE,MAAM,GAAG,MAAM,CAAA;KAAE,CAAC;IACpC,IAAI,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IAC1B,IAAI,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;CAC3B,CAAC;AAEN,MAAM,CAAC,OAAO,OAAO,YAAY;;IAE/B,MAAM,CAAC,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,GAAG,IAAI;IACzC,MAAM,CAAC,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,QAAQ,CAAC,EAAE,MAAM,GAAG,IAAI;IAC1D,MAAM,CAAC,IAAI,EAAE,MAAM,GAAG,IAAI;IAC1B,GAAG,CAAC,IAAI,EAAE,MAAM,GAAG,kBAAkB,GAAG,IAAI;IAC5C,MAAM,CAAC,IAAI,EAAE,MAAM,GAAG,kBAAkB,EAAE;IAC1C,GAAG,CAAC,IAAI,EAAE,MAAM,GAAG,OAAO;IAC1B,GAAG,CAAC,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,GAAG,IAAI;IACtC,GAAG,CAAC,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,QAAQ,CAAC,EAAE,MAAM,GAAG,IAAI;IAGvD,MAAM,CAAC,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE;QAAE,GAAG,EAAE,MAAM,CAAC;QAAC,IAAI,CAAC,EAAE,MAAM,CAAC;QAAC,IAAI,CAAC,EAAE,MAAM,CAAA;KAAE,GAAG,IAAI;IAChF,GAAG,CAAC,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE;QAAE,GAAG,EAAE,MAAM,CAAC;QAAC,IAAI,CAAC,EAAE,MAAM,CAAC;QAAC,IAAI,CAAC,EAAE,MAAM,CAAA;KAAE,GAAG,IAAI;IAG7E,OAAO,CACL,QAAQ,EAAE,CAAC,KAAK,EAAE,kBAAkB,EAAE,GAAG,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,KAAK,IAAI,EAC9E,OAAO,CAAC,EAAE,OAAO,GAChB,IAAI;IACP,IAAI,IAAI,gBAAgB,CAAC,MAAM,CAAC;IAChC,MAAM,IAAI,gBAAgB,CAAC,kBAAkB,CAAC;IAC9C,OAAO,IAAI,gBAAgB,CAAC,CAAC,MAAM,EAAE,kBAAkB,CAAC,CAAC;IACzD,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,gBAAgB,CAAC,CAAC,MAAM,EAAE,kBAAkB,CAAC,CAAC;CACpE;AAkCD,wBAAgB,oBAAoB,CAAC,QAAQ,EAAE,OAAO,QAAQ,GAAG,OAAO,YAAY,CAkInF"}