{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": "AAAA,OAAO,EAA0B,mBAAmB,EAAE,MAAM,mBAAmB,CAAC;AAChF,OAAO,EAAE,SAAS,EAAE,KAAK,EAAE,MAAM,OAAO,CAAC;AAEzC,OAAO,aAAa,MAAM,iBAAiB,CAAC;AAG5C,mFAAmF;AACnF,MAAM,CAAC,MAAM,gBAAgB,GAAG,yBAAyB,CAAC;AAE1D,yGAAyG;AACzG,MAAM,CAAC,KAAK,UAAU,gBAAgB;IACpC,IAAI,aAAa,CAAC,gBAAgB,EAAE;QAClC,OAAO,MAAM,aAAa,CAAC,gBAAgB,EAAE,CAAC;KAC/C;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAED;;;;;;;;GAQG;AACH,MAAM,UAAU,YAAY,CAAC,GAAY,EAAE,OAA0B;IACnE,MAAM,UAAU,GAAG,KAAK,EAAE,CAAC;IAC3B,MAAM,YAAY,GAAG,GAAG,IAAI,UAAU,CAAC;IAEvC,SAAS,CAAC,GAAG,EAAE;QACb,IAAI,SAAS,GAAG,IAAI,CAAC;QACrB,sBAAsB,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE;YAC7C,IAAI,SAAS,IAAI,aAAa,CAAC,iBAAiB,IAAI,OAAO,EAAE,QAAQ,EAAE;gBACrE,WAAW,CAAC,YAAY,EAAE,OAAO,CAAC,QAAQ,CAAC,CAAC;aAC7C;QACH,CAAC,CAAC,CAAC;QACH,OAAO,GAAG,EAAE;YACV,SAAS,GAAG,KAAK,CAAC;YAClB,IAAI,OAAO,EAAE,0BAA0B,EAAE;gBACvC,mBAAmB,CAAC,YAAY,CAAC,CAAC,KAAK,CAAC,GAAG,EAAE,GAAE,CAAC,CAAC,CAAC;aACnD;iBAAM;gBACL,mBAAmB,CAAC,YAAY,CAAC,CAAC;aACnC;QACH,CAAC,CAAC;IACJ,CAAC,EAAE,CAAC,YAAY,CAAC,CAAC,CAAC;AACrB,CAAC;AAED,cAAc;AACd;;;;;;;;;;;GAWG;AACH,MAAM,UAAU,iBAAiB,CAAC,MAAc,gBAAgB;IAC9D,OAAO,CAAC,IAAI,CAAC,0EAA0E,CAAC,CAAC;IACzF,OAAO,sBAAsB,CAAC,GAAG,CAAC,CAAC;AACrC,CAAC;AAED,cAAc;AACd;;;;;;;;;;GAUG;AACH,MAAM,CAAC,KAAK,UAAU,sBAAsB,CAAC,MAAc,gBAAgB;IACzE,MAAM,aAAa,CAAC,QAAQ,EAAE,CAAC,GAAG,CAAC,CAAC;AACtC,CAAC;AAED,cAAc;AACd;;;;;;GAMG;AACH,MAAM,CAAC,KAAK,UAAU,mBAAmB,CAAC,MAAc,gBAAgB;IACtE,MAAM,aAAa,CAAC,UAAU,EAAE,CAAC,GAAG,CAAC,CAAC;AACxC,CAAC;AAED;;;;;;;;;;;GAWG;AACH,MAAM,UAAU,WAAW,CACzB,aAAyC,EACzC,QAA4B;IAE5B,sCAAsC;IACtC,IAAI,CAAC,aAAa,CAAC,iBAAiB,EAAE;QACpC,MAAM,IAAI,mBAAmB,CAAC,eAAe,EAAE,mBAAmB,CAAC,CAAC;KACrE;IAED,MAAM,GAAG,GAAG,OAAO,aAAa,KAAK,QAAQ,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,gBAAgB,CAAC;IACjF,MAAM,SAAS,GAAG,OAAO,aAAa,KAAK,UAAU,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,QAAQ,CAAC;IAEjF,OAAO,aAAa,CAAC,iBAAiB,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC;AACzD,CAAC;AAED,cAAc,mBAAmB,CAAC", "sourcesContent": ["import { type EventSubscription, UnavailabilityError } from 'expo-modules-core';\nimport { useEffect, useId } from 'react';\n\nimport ExpoKeepAwake from './ExpoKeepAwake';\nimport { KeepAwakeListener, KeepAwakeOptions } from './KeepAwake.types';\n\n/** Default tag, used when no tag has been specified in keep awake method calls. */\nexport const ExpoKeepAwakeTag = 'ExpoKeepAwakeDefaultTag';\n\n/** @returns `true` on all platforms except [unsupported web browsers](https://caniuse.com/wake-lock). */\nexport async function isAvailableAsync(): Promise<boolean> {\n  if (ExpoKeepAwake.isAvailableAsync) {\n    return await ExpoKeepAwake.isAvailableAsync();\n  }\n  return true;\n}\n\n/**\n * A React hook to keep the screen awake for as long as the owner component is mounted.\n * The optionally provided `tag` argument is used when activating and deactivating the keep-awake\n * feature. If unspecified, an ID unique to the owner component is used. See the documentation for\n * `activateKeepAwakeAsync` below to learn more about the `tag` argument.\n *\n * @param tag Tag to lock screen sleep prevention. If not provided, an ID unique to the owner component is used.\n * @param options Additional options for the keep awake hook.\n */\nexport function useKeepAwake(tag?: string, options?: KeepAwakeOptions): void {\n  const defaultTag = useId();\n  const tagOrDefault = tag ?? defaultTag;\n\n  useEffect(() => {\n    let isMounted = true;\n    activateKeepAwakeAsync(tagOrDefault).then(() => {\n      if (isMounted && ExpoKeepAwake.addListenerForTag && options?.listener) {\n        addListener(tagOrDefault, options.listener);\n      }\n    });\n    return () => {\n      isMounted = false;\n      if (options?.suppressDeactivateWarnings) {\n        deactivateKeepAwake(tagOrDefault).catch(() => {});\n      } else {\n        deactivateKeepAwake(tagOrDefault);\n      }\n    };\n  }, [tagOrDefault]);\n}\n\n// @needsAudit\n/**\n * Prevents the screen from sleeping until `deactivateKeepAwake` is called with the same `tag` value.\n *\n * If the `tag` argument is specified, the screen will not sleep until you call `deactivateKeepAwake`\n * with the same `tag` argument. When using multiple `tags` for activation you'll have to deactivate\n * each one in order to re-enable screen sleep. If tag is unspecified, the default `tag` is used.\n *\n * Web support [is limited](https://caniuse.com/wake-lock).\n *\n * @param tag Tag to lock screen sleep prevention. If not provided, the default tag is used.\n * @deprecated use `activateKeepAwakeAsync` instead.\n */\nexport function activateKeepAwake(tag: string = ExpoKeepAwakeTag): Promise<void> {\n  console.warn('`activateKeepAwake` is deprecated. Use `activateKeepAwakeAsync` instead.');\n  return activateKeepAwakeAsync(tag);\n}\n\n// @needsAudit\n/**\n * Prevents the screen from sleeping until `deactivateKeepAwake` is called with the same `tag` value.\n *\n * If the `tag` argument is specified, the screen will not sleep until you call `deactivateKeepAwake`\n * with the same `tag` argument. When using multiple `tags` for activation you'll have to deactivate\n * each one in order to re-enable screen sleep. If tag is unspecified, the default `tag` is used.\n *\n * Web support [is limited](https://caniuse.com/wake-lock).\n *\n * @param tag Tag to lock screen sleep prevention. If not provided, the default tag is used.\n */\nexport async function activateKeepAwakeAsync(tag: string = ExpoKeepAwakeTag): Promise<void> {\n  await ExpoKeepAwake.activate?.(tag);\n}\n\n// @needsAudit\n/**\n * Releases the lock on screen-sleep prevention associated with the given `tag` value. If `tag`\n * is unspecified, it defaults to the same default tag that `activateKeepAwake` uses.\n *\n * @param tag Tag to release the lock on screen sleep prevention. If not provided,\n * the default tag is used.\n */\nexport async function deactivateKeepAwake(tag: string = ExpoKeepAwakeTag): Promise<void> {\n  await ExpoKeepAwake.deactivate?.(tag);\n}\n\n/**\n * Observe changes to the keep awake timer.\n * On web, this changes when navigating away from the active window/tab. No-op on native.\n * @platform web\n *\n * @example\n * ```ts\n * KeepAwake.addListener(({ state }) => {\n *   // ...\n * });\n * ```\n */\nexport function addListener(\n  tagOrListener: string | KeepAwakeListener,\n  listener?: KeepAwakeListener\n): EventSubscription {\n  // Assert so the type is non-nullable.\n  if (!ExpoKeepAwake.addListenerForTag) {\n    throw new UnavailabilityError('ExpoKeepAwake', 'addListenerForTag');\n  }\n\n  const tag = typeof tagOrListener === 'string' ? tagOrListener : ExpoKeepAwakeTag;\n  const _listener = typeof tagOrListener === 'function' ? tagOrListener : listener;\n\n  return ExpoKeepAwake.addListenerForTag(tag, _listener);\n}\n\nexport * from './KeepAwake.types';\n"]}