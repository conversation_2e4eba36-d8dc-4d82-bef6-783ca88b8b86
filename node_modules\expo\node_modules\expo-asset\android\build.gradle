apply plugin: 'com.android.library'

group = 'expo.modules.asset'
version = '11.0.5'

def expoModulesCorePlugin = new File(project(":expo-modules-core").projectDir.absolutePath, "ExpoModulesCorePlugin.gradle")
apply from: expoModulesCorePlugin
applyKotlinExpoModulesCorePlugin()
useCoreDependencies()
useDefaultAndroidSdkVersions()
useExpoPublishing()

android {
  namespace "expo.modules.asset"
  defaultConfig {
    versionCode 1
    versionName "11.0.5"
  }
}
