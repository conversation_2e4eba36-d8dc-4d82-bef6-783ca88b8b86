apply plugin: 'com.android.library'

group = 'host.exp.exponent'
version = '13.0.4'

def expoModulesCorePlugin = new File(project(":expo-modules-core").projectDir.absolutePath, "ExpoModulesCorePlugin.gradle")
apply from: expoModulesCorePlugin
applyKotlinExpoModulesCorePlugin()
useCoreDependencies()
useDefaultAndroidSdkVersions()
useExpoPublishing()

android {
  namespace "expo.modules.font"
  defaultConfig {
    versionCode 29
    versionName "13.0.4"
  }
}

dependencies {
  implementation 'com.facebook.react:react-android'
}
