{"version": 3, "file": "Font.js", "sourceRoot": "", "sources": ["../src/Font.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,UAAU,EAAE,QAAQ,EAAE,mBAAmB,EAAE,MAAM,mBAAmB,CAAC;AAE9E,OAAO,cAAc,MAAM,kBAAkB,CAAC;AAC9C,OAAO,EAAE,WAAW,EAA+C,MAAM,cAAc,CAAC;AACxF,OAAO,EAAE,iBAAiB,EAAE,mBAAmB,EAAE,MAAM,cAAc,CAAC;AACtE,OAAO,EACL,eAAe,EACf,cAAc,EACd,YAAY,EACZ,UAAU,EACV,UAAU,EACV,wBAAwB,GACzB,MAAM,UAAU,CAAC;AAClB,OAAO,EAAE,kBAAkB,EAAE,MAAM,UAAU,CAAC;AAE9C,cAAc;AACd;;;;;GAKG;AACH,MAAM,UAAU,QAAQ,CAAC,UAAkB;IACzC,IAAI,QAAQ,CAAC,EAAE,KAAK,KAAK,EAAE;QACzB,OAAO,eAAe,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;KAC7E;IACD,OAAO,cAAc,CAAC,UAAU,CAAC,CAAC;AACpC,CAAC;AAED;;;;;GAKG;AACH,MAAM,UAAU,cAAc;IAC5B,OAAO,cAAc,CAAC,cAAc,EAAE,CAAC;AACzC,CAAC;AAED,cAAc;AACd;;;;;GAKG;AACH,MAAM,UAAU,SAAS,CAAC,UAAkB;IAC1C,OAAO,UAAU,IAAI,YAAY,CAAC;AACpC,CAAC;AAED,cAAc;AACd;;;;;;;;;;;;;GAaG;AACH,MAAM,UAAU,SAAS,CACvB,mBAAwD,EACxD,MAAmB;IAEnB,uFAAuF;IACvF,qFAAqF;IACrF,iCAAiC;IACjC,MAAM,QAAQ,GAAG,QAAQ,CAAC,EAAE,KAAK,KAAK,IAAI,OAAO,MAAM,KAAK,WAAW,CAAC;IAExE,IAAI,OAAO,mBAAmB,KAAK,QAAQ,EAAE;QAC3C,IAAI,MAAM,EAAE;YACV,OAAO,OAAO,CAAC,MAAM,CACnB,IAAI,UAAU,CACZ,cAAc,EACd,sDAAsD,MAAM,0GAA0G,CACvK,CACF,CAAC;SACH;QACD,MAAM,OAAO,GAAG,mBAAmB,CAAC;QACpC,MAAM,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAEnC,IAAI,QAAQ,EAAE;YACZ,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,kBAAkB,CAAC,IAAI,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC7D,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;SAC1B;QAED,OAAO,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,wBAAwB,CAAC,IAAI,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CACzF,GAAG,EAAE,GAAE,CAAC,CACT,CAAC;KACH;IAED,IAAI,QAAQ,EAAE;QACZ,kBAAkB,CAAC,mBAAmB,EAAE,MAAM,CAAC,CAAC;QAChD,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;KAC1B;IAED,OAAO,wBAAwB,CAAC,mBAAmB,EAAE,MAAM,CAAC,CAAC;AAC/D,CAAC;AAED,KAAK,UAAU,wBAAwB,CACrC,UAAkB,EAClB,MAA0B;IAE1B,IAAI,CAAC,MAAM,EAAE;QACX,MAAM,IAAI,UAAU,CAClB,iBAAiB,EACjB,iDAAiD,UAAU,MAAM,MAAM,sEAAsE,UAAU,GAAG,CAC3J,CAAC;KACH;IAED,oEAAoE;IACpE,0FAA0F;IAC1F,IAAI,QAAQ,CAAC,UAAU,CAAC,EAAE;QACxB,OAAO;KACR;IAED,IAAI,YAAY,CAAC,cAAc,CAAC,UAAU,CAAC,EAAE;QAC3C,OAAO,YAAY,CAAC,UAAU,CAAC,CAAC;KACjC;IAED,+FAA+F;IAC/F,2FAA2F;IAC3F,iGAAiG;IACjG,wBAAwB;IAExB,MAAM,KAAK,GAAG,iBAAiB,CAAC,MAAM,CAAC,CAAC;IACxC,YAAY,CAAC,UAAU,CAAC,GAAG,CAAC,KAAK,IAAI,EAAE;QACrC,IAAI;YACF,MAAM,mBAAmB,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;YAC7C,UAAU,CAAC,UAAU,CAAC,CAAC;SACxB;gBAAS;YACR,OAAO,YAAY,CAAC,UAAU,CAAC,CAAC;SACjC;IACH,CAAC,CAAC,EAAE,CAAC;IAEL,MAAM,YAAY,CAAC,UAAU,CAAC,CAAC;AACjC,CAAC;AAED,cAAc;AACd;;;GAGG;AACH,MAAM,CAAC,KAAK,UAAU,cAAc;IAClC,IAAI,CAAC,cAAc,CAAC,cAAc,EAAE;QAClC,MAAM,IAAI,mBAAmB,CAAC,WAAW,EAAE,gBAAgB,CAAC,CAAC;KAC9D;IAED,IAAI,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,MAAM,EAAE;QACpC,MAAM,IAAI,UAAU,CAClB,YAAY,EACZ,oDAAoD,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAC3F,CAAC;KACH;IACD,UAAU,EAAE,CAAC;IACb,MAAM,cAAc,CAAC,cAAc,EAAE,CAAC;AACxC,CAAC;AAED,cAAc;AACd;;;;;;;;GAQG;AACH,MAAM,CAAC,KAAK,UAAU,WAAW,CAC/B,mBAA+D,EAC/D,OAA2B;IAE3B,IAAI,CAAC,cAAc,CAAC,WAAW,EAAE;QAC/B,MAAM,IAAI,mBAAmB,CAAC,WAAW,EAAE,aAAa,CAAC,CAAC;KAC3D;IACD,IAAI,OAAO,mBAAmB,KAAK,QAAQ,EAAE;QAC3C,IAAI,OAAO,EAAE;YACX,MAAM,IAAI,UAAU,CAClB,cAAc,EACd,uDAAuD,OAAO,4GAA4G,CAC3K,CAAC;SACH;QACD,MAAM,OAAO,GAAG,mBAAmB,CAAC;QACpC,MAAM,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACnC,MAAM,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,0BAA0B,CAAC,IAAI,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;QACxF,OAAO;KACR;IAED,OAAO,MAAM,0BAA0B,CAAC,mBAAmB,EAAE,OAAO,CAAC,CAAC;AACxE,CAAC;AAED,KAAK,UAAU,0BAA0B,CACvC,UAAkB,EAClB,OAAkC;IAElC,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE;QACzB,OAAO;KACR;SAAM;QACL,wBAAwB,CAAC,UAAU,CAAC,CAAC;KACtC;IAED,+FAA+F;IAC/F,2FAA2F;IAC3F,iGAAiG;IACjG,wBAAwB;IAExB,IAAI,CAAC,UAAU,EAAE;QACf,MAAM,IAAI,UAAU,CAAC,iBAAiB,EAAE,6BAA6B,CAAC,CAAC;KACxE;IAED,MAAM,cAAc,CAAC,WAAW,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;AACxD,CAAC;AAED,OAAO,EAAE,WAAW,EAA+C,CAAC", "sourcesContent": ["import { CodedError, Platform, UnavailabilityError } from 'expo-modules-core';\n\nimport ExpoFontLoader from './ExpoFontLoader';\nimport { FontDisplay, FontSource, FontResource, UnloadFontOptions } from './Font.types';\nimport { getAssetForSource, loadSingleFontAsync } from './FontLoader';\nimport {\n  isLoadedInCache,\n  isLoadedNative,\n  loadPromises,\n  markLoaded,\n  purgeCache,\n  purgeFontFamilyFromCache,\n} from './memory';\nimport { registerStaticFont } from './server';\n\n// @needsAudit\n/**\n * Synchronously detect if the font for `fontFamily` has finished loading.\n *\n * @param fontFamily The name used to load the `FontResource`.\n * @return Returns `true` if the font has fully loaded.\n */\nexport function isLoaded(fontFamily: string): boolean {\n  if (Platform.OS === 'web') {\n    return isLoadedInCache(fontFamily) || !!ExpoFontLoader.isLoaded(fontFamily);\n  }\n  return isLoadedNative(fontFamily);\n}\n\n/**\n * Synchronously get all the fonts that have been loaded.\n * This includes fonts that were bundled at build time using the config plugin, as well as those loaded at runtime using `loadAsync`.\n *\n * @returns Returns array of strings which you can use as `fontFamily` [style prop](https://reactnative.dev/docs/text#style).\n */\nexport function getLoadedFonts(): string[] {\n  return ExpoFontLoader.getLoadedFonts();\n}\n\n// @needsAudit\n/**\n * Synchronously detect if the font for `fontFamily` is still being loaded.\n *\n * @param fontFamily The name used to load the `FontResource`.\n * @returns Returns `true` if the font is still loading.\n */\nexport function isLoading(fontFamily: string): boolean {\n  return fontFamily in loadPromises;\n}\n\n// @needsAudit\n/**\n * An efficient method for loading fonts from static or remote resources which can then be used\n * with the platform's native text elements. In the browser, this generates a `@font-face` block in\n * a shared style sheet for fonts. No CSS is needed to use this method.\n *\n * > **Note**: We recommend using the [config plugin](#configuration-in-appjsonappconfigjs) instead whenever possible.\n *\n * @param fontFamilyOrFontMap String or map of values that can be used as the `fontFamily` [style prop](https://reactnative.dev/docs/text#style)\n * with React Native `Text` elements.\n * @param source The font asset that should be loaded into the `fontFamily` namespace.\n *\n * @return Returns a promise that fulfils when the font has loaded. Often you may want to wrap the\n * method in a `try/catch/finally` to ensure the app continues if the font fails to load.\n */\nexport function loadAsync(\n  fontFamilyOrFontMap: string | Record<string, FontSource>,\n  source?: FontSource\n): Promise<void> {\n  // NOTE(EvanBacon): Static render pass on web must be synchronous to collect all fonts.\n  // Because of this, `loadAsync` doesn't use the `async` keyword and deviates from the\n  // standard Expo SDK style guide.\n  const isServer = Platform.OS === 'web' && typeof window === 'undefined';\n\n  if (typeof fontFamilyOrFontMap === 'object') {\n    if (source) {\n      return Promise.reject(\n        new CodedError(\n          `ERR_FONT_API`,\n          `No fontFamily can be used for the provided source: ${source}. The second argument of \\`loadAsync()\\` can only be used with a \\`string\\` value as the first argument.`\n        )\n      );\n    }\n    const fontMap = fontFamilyOrFontMap;\n    const names = Object.keys(fontMap);\n\n    if (isServer) {\n      names.map((name) => registerStaticFont(name, fontMap[name]));\n      return Promise.resolve();\n    }\n\n    return Promise.all(names.map((name) => loadFontInNamespaceAsync(name, fontMap[name]))).then(\n      () => {}\n    );\n  }\n\n  if (isServer) {\n    registerStaticFont(fontFamilyOrFontMap, source);\n    return Promise.resolve();\n  }\n\n  return loadFontInNamespaceAsync(fontFamilyOrFontMap, source);\n}\n\nasync function loadFontInNamespaceAsync(\n  fontFamily: string,\n  source?: FontSource | null\n): Promise<void> {\n  if (!source) {\n    throw new CodedError(\n      `ERR_FONT_SOURCE`,\n      `Cannot load null or undefined font source: { \"${fontFamily}\": ${source} }. Expected asset of type \\`FontSource\\` for fontFamily of name: \"${fontFamily}\"`\n    );\n  }\n\n  // we consult the native module to see if the font is already loaded\n  // this is slower than checking the cache but can help avoid loading the same font n times\n  if (isLoaded(fontFamily)) {\n    return;\n  }\n\n  if (loadPromises.hasOwnProperty(fontFamily)) {\n    return loadPromises[fontFamily];\n  }\n\n  // Important: we want all callers that concurrently try to load the same font to await the same\n  // promise. If we're here, we haven't created the promise yet. To ensure we create only one\n  // promise in the program, we need to create the promise synchronously without yielding the event\n  // loop from this point.\n\n  const asset = getAssetForSource(source);\n  loadPromises[fontFamily] = (async () => {\n    try {\n      await loadSingleFontAsync(fontFamily, asset);\n      markLoaded(fontFamily);\n    } finally {\n      delete loadPromises[fontFamily];\n    }\n  })();\n\n  await loadPromises[fontFamily];\n}\n\n// @needsAudit\n/**\n * Unloads all the custom fonts. This is used for testing.\n * @hidden\n */\nexport async function unloadAllAsync(): Promise<void> {\n  if (!ExpoFontLoader.unloadAllAsync) {\n    throw new UnavailabilityError('expo-font', 'unloadAllAsync');\n  }\n\n  if (Object.keys(loadPromises).length) {\n    throw new CodedError(\n      `ERR_UNLOAD`,\n      `Cannot unload fonts while they're still loading: ${Object.keys(loadPromises).join(', ')}`\n    );\n  }\n  purgeCache();\n  await ExpoFontLoader.unloadAllAsync();\n}\n\n// @needsAudit\n/**\n * Unload custom fonts matching the `fontFamily`s and display values provided.\n * This is used for testing.\n *\n * @param fontFamilyOrFontMap The name or names of the custom fonts that will be unloaded.\n * @param options When `fontFamilyOrFontMap` is a string, this should be the font source used to load\n * the custom font originally.\n * @hidden\n */\nexport async function unloadAsync(\n  fontFamilyOrFontMap: string | Record<string, UnloadFontOptions>,\n  options?: UnloadFontOptions\n): Promise<void> {\n  if (!ExpoFontLoader.unloadAsync) {\n    throw new UnavailabilityError('expo-font', 'unloadAsync');\n  }\n  if (typeof fontFamilyOrFontMap === 'object') {\n    if (options) {\n      throw new CodedError(\n        `ERR_FONT_API`,\n        `No fontFamily can be used for the provided options: ${options}. The second argument of \\`unloadAsync()\\` can only be used with a \\`string\\` value as the first argument.`\n      );\n    }\n    const fontMap = fontFamilyOrFontMap;\n    const names = Object.keys(fontMap);\n    await Promise.all(names.map((name) => unloadFontInNamespaceAsync(name, fontMap[name])));\n    return;\n  }\n\n  return await unloadFontInNamespaceAsync(fontFamilyOrFontMap, options);\n}\n\nasync function unloadFontInNamespaceAsync(\n  fontFamily: string,\n  options?: UnloadFontOptions | null\n): Promise<void> {\n  if (!isLoaded(fontFamily)) {\n    return;\n  } else {\n    purgeFontFamilyFromCache(fontFamily);\n  }\n\n  // Important: we want all callers that concurrently try to load the same font to await the same\n  // promise. If we're here, we haven't created the promise yet. To ensure we create only one\n  // promise in the program, we need to create the promise synchronously without yielding the event\n  // loop from this point.\n\n  if (!fontFamily) {\n    throw new CodedError(`ERR_FONT_FAMILY`, `Cannot unload an empty name`);\n  }\n\n  await ExpoFontLoader.unloadAsync(fontFamily, options);\n}\n\nexport { FontDisplay, FontSource, FontResource, UnloadFontOptions };\n"]}