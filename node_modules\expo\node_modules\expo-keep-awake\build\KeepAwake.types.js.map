{"version": 3, "file": "KeepAwake.types.js", "sourceRoot": "", "sources": ["../src/KeepAwake.types.ts"], "names": [], "mappings": "AAMA,cAAc;AACd,MAAM,CAAN,IAAY,mBAEX;AAFD,WAAY,mBAAmB;IAC7B,0CAAmB,CAAA;AACrB,CAAC,EAFW,mBAAmB,KAAnB,mBAAmB,QAE9B", "sourcesContent": ["// @needsAudit\nexport type KeepAwakeEvent = {\n  /** Keep awake state. */\n  state: KeepAwakeEventState;\n};\n\n// @needsAudit\nexport enum KeepAwakeEventState {\n  RELEASE = 'release',\n}\n\n// @needsAudit\n/**\n * @platform web\n */\nexport type KeepAwakeListener = (event: KeepAwakeEvent) => void;\n\nexport type KeepAwakeOptions = {\n  /**\n   * The call will throw an unhandled promise rejection on Android when the original Activity is dead or deactivated.\n   * Set the value to `true` for suppressing the uncaught exception.\n   */\n  suppressDeactivateWarnings?: boolean;\n\n  /**\n   * A callback that is invoked when the keep-awake state changes.\n   * @platform web\n   */\n  listener?: KeepAwakeListener;\n};\n"]}