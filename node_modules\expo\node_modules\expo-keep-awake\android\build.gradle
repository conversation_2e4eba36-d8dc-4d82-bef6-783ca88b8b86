apply plugin: 'com.android.library'

group = 'host.exp.exponent'
version = '14.0.3'

def expoModulesCorePlugin = new File(project(":expo-modules-core").projectDir.absolutePath, "ExpoModulesCorePlugin.gradle")
apply from: expoModulesCorePlugin
applyKotlinExpoModulesCorePlugin()
useCoreDependencies()
useDefaultAndroidSdkVersions()
useExpoPublishing()

android {
  namespace "expo.modules.keepawake"
  defaultConfig {
    versionCode 16
    versionName "14.0.3"
  }
}
