{"version": 3, "file": "WebSocketWithReconnect.d.ts", "sourceRoot": "", "sources": ["../../src/devtools/WebSocketWithReconnect.ts"], "names": [], "mappings": "AAEA,OAAO,KAAK,EAAE,2BAA2B,EAAE,MAAM,kBAAkB,CAAC;AAEpE,MAAM,WAAW,OAAO;IACtB;;;OAGG;IACH,eAAe,CAAC,EAAE,MAAM,CAAC;IAEzB;;;OAGG;IACH,UAAU,CAAC,EAAE,MAAM,CAAC;IAEpB;;OAEG;IACH,cAAc,CAAC,EAAE,MAAM,CAAC;IAExB;;;OAGG;IACH,OAAO,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,KAAK,IAAI,CAAC;IAEjC;;;OAGG;IACH,WAAW,CAAC,EAAE,CAAC,MAAM,EAAE,MAAM,KAAK,IAAI,CAAC;IAEvC;;OAEG;IACH,UAAU,CAAC,EAAE,2BAA2B,CAAC,qBAAqB,CAAC,CAAC;CACjE;AAED,qBAAa,sBAAuB,YAAW,SAAS;aAmBpC,GAAG,EAAE,MAAM;IAlB7B,OAAO,CAAC,QAAQ,CAAC,eAAe,CAAS;IACzC,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAS;IACpC,OAAO,CAAC,QAAQ,CAAC,cAAc,CAAS;IACxC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAyB;IACjD,OAAO,CAAC,QAAQ,CAAC,WAAW,CAA2B;IAEvD,OAAO,CAAC,EAAE,CAA0B;IACpC,OAAO,CAAC,OAAO,CAAK;IACpB,OAAO,CAAC,oBAAoB,CAA8C;IAC1E,OAAO,CAAC,QAAQ,CAAS;IACzB,OAAO,CAAC,SAAS,CAA6D;IAC9E,OAAO,CAAC,cAAc,CAAqE;IAE3F,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAsB;IAC9C,OAAO,CAAC,QAAQ,CAAC,kBAAkB,CAA2B;IAC9D,OAAO,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAwB;gBAGpC,GAAG,EAAE,MAAM,EAC3B,OAAO,CAAC,EAAE,OAAO;IAgBZ,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,MAAM,CAAC,EAAE,MAAM;IAqBpC,gBAAgB,CAAC,KAAK,EAAE,SAAS,EAAE,QAAQ,EAAE,CAAC,KAAK,EAAE,qBAAqB,KAAK,IAAI,GAAG,IAAI;IAC1F,gBAAgB,CAAC,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,IAAI,GAAG,IAAI;IAC3D,gBAAgB,CAAC,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,CAAC,KAAK,EAAE,mBAAmB,KAAK,IAAI,GAAG,IAAI;IACtF,gBAAgB,CAAC,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,CAAC,KAAK,EAAE,mBAAmB,KAAK,IAAI,GAAG,IAAI;IAKtF,mBAAmB,CAAC,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,CAAC,KAAK,EAAE,GAAG,KAAK,IAAI;IAYzE,OAAO,CAAC,OAAO;IAiBR,IAAI,CAAC,IAAI,EAAE,MAAM,GAAG,eAAe,GAAG,IAAI,GAAG,eAAe,GAAG,IAAI;IAqB1E,OAAO,CAAC,UAAU,CAUhB;IAEF,OAAO,CAAC,aAAa,CAEnB;IAEF,OAAO,CAAC,WAAW,CAIjB;IAEF,OAAO,CAAC,WAAW,CAQjB;IAEF,OAAO,CAAC,oBAAoB,CAE1B;IAEF,OAAO,CAAC,2BAA2B;IAOnC,OAAO,CAAC,iBAAiB;IAsBzB,OAAO,CAAC,OAAO;IAgBf,IAAW,UAAU,WAWpB;IAMD,SAAgB,UAAU,KAAK;IAC/B,SAAgB,IAAI,KAAK;IACzB,SAAgB,OAAO,KAAK;IAC5B,SAAgB,MAAM,KAAK;IAE3B,IAAW,UAAU,eAEpB;IAED,IAAW,cAAc,WAExB;IAED,IAAW,UAAU,WAEpB;IAED,IAAW,QAAQ,WAElB;IAEM,IAAI,IAAI,IAAI;IAIZ,aAAa,CAAC,KAAK,EAAE,KAAK;IAQjC,IAAW,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,EAAE,mBAAmB,KAAK,GAAG,CAAC,GAAG,IAAI,EAElE;IAED,IAAW,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,KAAK,GAAG,CAAC,GAAG,IAAI,EAEpD;IAED,IAAW,SAAS,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,EAAE,qBAAqB,KAAK,GAAG,CAAC,GAAG,IAAI,EAEtE;IAED,IAAW,MAAM,CAAC,MAAM,EAAE,CAAC,MAAM,GAAG,CAAC,GAAG,IAAI,EAE3C;CAGF"}