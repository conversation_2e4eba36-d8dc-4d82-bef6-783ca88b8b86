{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": ";;;;;AAAA,0DAAkC;AAClC,gDAAwB;AAExB,+CASuB;AACvB,2DAAgG;AAShG;;GAEG;AACH,SAAS,qBAAqB,CAC5B,WAAmB,EACnB,EAAwD;IAExD,OAAO,mBAAS;SACb,OAAO,CAAC,GAAG,WAAW,aAAa,CAAC;SACpC,MAAM,CACL,qCAAqC,EACrC,8CAA8C,EAC9C,CAAC,KAAK,EAAE,QAAQ,EAAE,EAAE,CAAC,CAAC,QAAQ,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CACpD;SACA,MAAM,CACL,4BAA4B,EAC5B,uDAAuD,EACvD,CAAC,KAAK,EAAE,QAAQ,EAAE,EAAE,CAAC,CAAC,QAAQ,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CACpD;SACA,MAAM,CACL,2BAA2B,EAC3B,6FAA6F,EAC7F,OAAO,CACR;SACA,MAAM,CAAC,UAAU,EAAE,6BAA6B,CAAC;SACjD,SAAS,CACR,IAAI,mBAAS,CAAC,MAAM,CAClB,8BAA8B,EAC9B,qCAAqC,CACtC,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,eAAe,CAAC,CAC1C;SACA,MAAM,CACL,qBAAqB,EACrB,yEAAyE,EACzE,IAAI,CACL;SACA,MAAM,CAAC,wBAAwB,EAAE,iCAAiC,EAAE,KAAK,CAAC;SAC1E,MAAM,CAAC,KAAK,EAAE,WAAW,EAAE,eAAe,EAAE,EAAE;QAC7C,MAAM,OAAO,GAAG,MAAM,IAAA,sCAAwB,EAC5C,WAAW,CAAC,MAAM,GAAG,CAAC;YACpB,CAAC,CAAC;gBACE,GAAG,eAAe;gBAClB,WAAW;aACZ;YACH,CAAC,CAAC,eAAe,CACpB,CAAC;QACF,MAAM,aAAa,GAAG,MAAM,IAAA,8BAAgB,EAAC,OAAO,CAAC,CAAC;QACtD,OAAO,MAAM,EAAE,CAAC,aAAa,EAAE,OAAO,CAAC,CAAC;IAC1C,CAAC,CAAC,CAAC;AACP,CAAC;AAED;;GAEG;AACH,SAAS,sBAAsB,CAC7B,WAAmB,EACnB,EAAwD;IAExD,OAAO,qBAAqB,CAAc,WAAW,EAAE,EAAE,CAAC,CAAC;AAC7D,CAAC;AAED;;GAEG;AACH,SAAS,gCAAgC;IACvC,OAAO,mBAAS;SACb,OAAO,CAAC,gCAAgC,CAAC;SACzC,MAAM,CACL,2BAA2B,EAC3B,2FAA2F,EAC3F,KAAK,CACN;SACA,SAAS,CACR,IAAI,mBAAS,CAAC,MAAM,CAClB,8BAA8B,EAC9B,qCAAqC,CACtC,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,eAAe,CAAC,CAC1C;SACA,MAAM,CAAU,YAAY,EAAE,0CAA0C,EAAE,GAAG,EAAE,CAAC,IAAI,EAAE,KAAK,CAAC;SAC5F,MAAM,CAAC,KAAK,EAAE,WAAW,EAAE,eAAe,EAAE,EAAE;QAC7C,IAAI,CAAC,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC,QAAQ,CAAC,eAAe,CAAC,QAAQ,CAAC,EAAE;YAC1D,MAAM,IAAI,KAAK,CAAC,yBAAyB,eAAe,CAAC,QAAQ,EAAE,CAAC,CAAC;SACtE;QACD,MAAM,WAAW,GAAG,cAAI,CAAC,OAAO,CAC9B,MAAM,IAAA,4CAA8B,EAAC,eAAe,CAAC,WAAW,CAAC,CAClE,CAAC;QACF,MAAM,cAAc,GAAG,MAAM,IAAA,sCAAwB,EACnD,WAAW,CAAC,MAAM,GAAG,CAAC;YACpB,CAAC,CAAC;gBACE,GAAG,eAAe;gBAClB,WAAW;gBACX,WAAW;aACZ;YACH,CAAC,CAAC;gBACE,GAAG,eAAe;gBAClB,WAAW;aACZ,CACN,CAAC;QACF,MAAM,OAAO,GAA2B;YACtC,QAAQ,EAAE,cAAc,CAAC,QAAQ;YACjC,WAAW;YACX,WAAW,EAAE,cAAc,CAAC,WAAW;SACxC,CAAC;QACF,MAAM,OAAO,GAAG,MAAM,IAAA,gDAA4B,EAAC,OAAO,CAAC,CAAC;QAC5D,IAAI,eAAe,CAAC,IAAI,EAAE;YACxB,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC;SACtC;aAAM;YACL,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC;SAClE;IACH,CAAC,CAAC,CAAC;AACP,CAAC;AAED,MAAM,CAAC,OAAO,GAAG,KAAK,WAAW,IAAc;IAC7C,uCAAuC;IACvC,qBAAqB,CAAqC,QAAQ,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,EAAE;QAC7F,IAAI,OAAO,CAAC,IAAI,EAAE;YAChB,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC;SACtC;aAAM;YACL,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC;SAClE;IACH,CAAC,CAAC,CAAC,MAAM,CAAU,YAAY,EAAE,0CAA0C,EAAE,GAAG,EAAE,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;IAEhG,qEAAqE;IACrE,qBAAqB,CAAC,QAAQ,EAAE,CAAC,OAAO,EAAE,OAAO,EAAE,EAAE;QACnD,MAAM,kBAAkB,GAAG,IAAA,iCAAmB,EAAC,OAAO,EAAE,OAAO,CAAC,CAAC;QACjE,IAAI,CAAC,kBAAkB,EAAE;YACvB,OAAO,CAAC,GAAG,CAAC,uBAAuB,CAAC,CAAC;SACtC;IACH,CAAC,CAAC,CAAC;IAEH,mFAAmF;IACnF,sBAAsB,CAAC,SAAS,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,EAAE;QAC3D,MAAM,OAAO,GAAG,MAAM,IAAA,iCAAmB,EAAC,OAAO,EAAE,OAAO,CAAC,CAAC;QAC5D,MAAM,iBAAiB,GAAG,MAAM,IAAA,gDAAkC,EAAC,OAAO,CAAC,CAAC;QAE5E,IAAI,OAAO,CAAC,IAAI,EAAE;YAChB,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,iBAAiB,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC;SAC7D;aAAM;YACL,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,EAAE,iBAAiB,EAAE,OAAO,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC;SACzF;IACH,CAAC,CAAC,CAAC,MAAM,CAAU,YAAY,EAAE,0CAA0C,EAAE,GAAG,EAAE,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;IAEhG,wDAAwD;IACxD,gFAAgF;IAChF,sBAAsB,CAAkB,uBAAuB,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,EAAE;QAC1F,MAAM,OAAO,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,IAAA,iCAAmB,EAAC,OAAO,EAAE,OAAO,CAAC,CAAC;QACjF,IAAA,sCAAwB,EAAC,OAAO,EAAE,OAAO,CAAC,CAAC;IAC7C,CAAC,CAAC;SACC,MAAM,CACL,qBAAqB,EACrB,uEAAuE,CACxE;SACA,MAAM,CACL,6BAA6B,EAC7B,kEAAkE,CACnE;SACA,MAAM,CACL,SAAS,EACT,8FAA8F,EAC9F,KAAK,CACN,CAAC;IAEJ,uEAAuE;IACvE,sBAAsB,CACpB,2BAA2B,EAC3B,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,EAAE;QACzB,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,IAAI,EAAE,CAAC;QACxC,MAAM,OAAO,GAAG,MAAM,IAAA,iCAAmB,EAAC,OAAO,EAAE,OAAO,CAAC,CAAC;QAC5D,MAAM,eAAe,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC;QAE1F,IAAA,0CAA4B,EAAC,eAAe,EAAE,OAAO,CAAC,CAAC;IACzD,CAAC,CACF;SACE,MAAM,CACL,qBAAqB,EACrB,uEAAuE,CACxE;SACA,MAAM,CAAC,sBAAsB,EAAE,mDAAmD,CAAC;SACnF,MAAM,CACL,8BAA8B,EAC9B,qEAAqE,CACtE,CAAC;IAEJ,gCAAgC,EAAE,CAAC;IAEnC,MAAM,mBAAS;SACZ,OAAO,CAAC,OAAO,CAAC,uCAAuC,CAAC,CAAC,OAAO,CAAC;SACjE,WAAW,CAAC,8DAA8D,CAAC;SAC3E,UAAU,CAAC,IAAI,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC,CAAC;AACxC,CAAC,CAAC", "sourcesContent": ["import commander from 'commander';\nimport path from 'path';\n\nimport {\n  findModulesAsync,\n  generateModulesProviderAsync,\n  generatePackageListAsync,\n  getProjectPackageJsonPathAsync,\n  mergeLinkingOptionsAsync,\n  resolveExtraBuildDependenciesAsync,\n  resolveModulesAsync,\n  verifySearchResults,\n} from './autolinking';\nimport { type RNConfigCommandOptions, createReactNativeConfigAsync } from './reactNativeConfig';\nimport type {\n  GenerateModulesProviderOptions,\n  GenerateOptions,\n  ResolveOptions,\n  SearchOptions,\n  SearchResults,\n} from './types';\n\n/**\n * Registers a command that only searches for available expo modules.\n */\nfunction registerSearchCommand<OptionsType extends SearchOptions>(\n  commandName: string,\n  fn: (search: SearchResults, options: OptionsType) => any\n) {\n  return commander\n    .command(`${commandName} [paths...]`)\n    .option<string[] | null>(\n      '-i, --ignore-paths <ignorePaths...>',\n      'Paths to ignore when looking up for modules.',\n      (value, previous) => (previous ?? []).concat(value)\n    )\n    .option<string[] | null>(\n      '-e, --exclude <exclude...>',\n      'Package names to exclude when looking up for modules.',\n      (value, previous) => (previous ?? []).concat(value)\n    )\n    .option(\n      '-p, --platform [platform]',\n      'The platform that the resulting modules must support. Available options: \"apple\", \"android\"',\n      'apple'\n    )\n    .option('--silent', 'Silence resolution warnings')\n    .addOption(\n      new commander.Option(\n        '--project-root <projectRoot>',\n        'The path to the root of the project'\n      ).default(process.cwd(), 'process.cwd()')\n    )\n    .option(\n      '--only-project-deps',\n      'For a monorepo, include only modules that are the project dependencies.',\n      true\n    )\n    .option('--no-only-project-deps', 'Opposite of --only-project-deps', false)\n    .action(async (searchPaths, providedOptions) => {\n      const options = await mergeLinkingOptionsAsync<OptionsType>(\n        searchPaths.length > 0\n          ? {\n              ...providedOptions,\n              searchPaths,\n            }\n          : providedOptions\n      );\n      const searchResults = await findModulesAsync(options);\n      return await fn(searchResults, options);\n    });\n}\n\n/**\n * Registers a command that searches for modules and then resolves them for specific platform.\n */\nfunction registerResolveCommand<OptionsType extends ResolveOptions>(\n  commandName: string,\n  fn: (search: SearchResults, options: OptionsType) => any\n) {\n  return registerSearchCommand<OptionsType>(commandName, fn);\n}\n\n/**\n * Registry the `react-native-config` command.\n */\nfunction registerReactNativeConfigCommand() {\n  return commander\n    .command('react-native-config [paths...]')\n    .option(\n      '-p, --platform [platform]',\n      'The platform that the resulting modules must support. Available options: \"android\", \"ios\"',\n      'ios'\n    )\n    .addOption(\n      new commander.Option(\n        '--project-root <projectRoot>',\n        'The path to the root of the project'\n      ).default(process.cwd(), 'process.cwd()')\n    )\n    .option<boolean>('-j, --json', 'Output results in the plain JSON format.', () => true, false)\n    .action(async (searchPaths, providedOptions) => {\n      if (!['android', 'ios'].includes(providedOptions.platform)) {\n        throw new Error(`Unsupported platform: ${providedOptions.platform}`);\n      }\n      const projectRoot = path.dirname(\n        await getProjectPackageJsonPathAsync(providedOptions.projectRoot)\n      );\n      const linkingOptions = await mergeLinkingOptionsAsync<SearchOptions>(\n        searchPaths.length > 0\n          ? {\n              ...providedOptions,\n              projectRoot,\n              searchPaths,\n            }\n          : {\n              ...providedOptions,\n              projectRoot,\n            }\n      );\n      const options: RNConfigCommandOptions = {\n        platform: linkingOptions.platform,\n        projectRoot,\n        searchPaths: linkingOptions.searchPaths,\n      };\n      const results = await createReactNativeConfigAsync(options);\n      if (providedOptions.json) {\n        console.log(JSON.stringify(results));\n      } else {\n        console.log(require('util').inspect(results, false, null, true));\n      }\n    });\n}\n\nmodule.exports = async function (args: string[]) {\n  // Searches for available expo modules.\n  registerSearchCommand<SearchOptions & { json?: boolean }>('search', async (results, options) => {\n    if (options.json) {\n      console.log(JSON.stringify(results));\n    } else {\n      console.log(require('util').inspect(results, false, null, true));\n    }\n  }).option<boolean>('-j, --json', 'Output results in the plain JSON format.', () => true, false);\n\n  // Checks whether there are no resolving issues in the current setup.\n  registerSearchCommand('verify', (results, options) => {\n    const numberOfDuplicates = verifySearchResults(results, options);\n    if (!numberOfDuplicates) {\n      console.log('✅ Everything is fine!');\n    }\n  });\n\n  // Searches for available expo modules and resolves the results for given platform.\n  registerResolveCommand('resolve', async (results, options) => {\n    const modules = await resolveModulesAsync(results, options);\n    const extraDependencies = await resolveExtraBuildDependenciesAsync(options);\n\n    if (options.json) {\n      console.log(JSON.stringify({ extraDependencies, modules }));\n    } else {\n      console.log(require('util').inspect({ extraDependencies, modules }, false, null, true));\n    }\n  }).option<boolean>('-j, --json', 'Output results in the plain JSON format.', () => true, false);\n\n  // Generates a source file listing all packages to link.\n  // It's deprecated for apple platforms, use `generate-modules-provider` instead.\n  registerResolveCommand<GenerateOptions>('generate-package-list', async (results, options) => {\n    const modules = options.empty ? [] : await resolveModulesAsync(results, options);\n    generatePackageListAsync(modules, options);\n  })\n    .option(\n      '-t, --target <path>',\n      'Path to the target file, where the package list should be written to.'\n    )\n    .option(\n      '-n, --namespace <namespace>',\n      'Java package name under which the package list should be placed.'\n    )\n    .option(\n      '--empty',\n      'Whether to only generate an empty list. Might be used when the user opts-out of autolinking.',\n      false\n    );\n\n  // Generates a source file listing all packages to link in the runtime.\n  registerResolveCommand<GenerateModulesProviderOptions>(\n    'generate-modules-provider',\n    async (results, options) => {\n      const packages = options.packages ?? [];\n      const modules = await resolveModulesAsync(results, options);\n      const filteredModules = modules.filter((module) => packages.includes(module.packageName));\n\n      generateModulesProviderAsync(filteredModules, options);\n    }\n  )\n    .option(\n      '-t, --target <path>',\n      'Path to the target file, where the package list should be written to.'\n    )\n    .option('--entitlement <path>', 'Path to the Apple code signing entitlements file.')\n    .option(\n      '-p, --packages <packages...>',\n      'Names of the packages to include in the generated modules provider.'\n    );\n\n  registerReactNativeConfigCommand();\n\n  await commander\n    .version(require('expo-modules-autolinking/package.json').version)\n    .description('CLI command that searches for Expo modules to autolink them.')\n    .parseAsync(args, { from: 'user' });\n};\n"]}