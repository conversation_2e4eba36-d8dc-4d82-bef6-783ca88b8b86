{"@dev-plugins/apollo-client": "~0.2.0", "@dev-plugins/async-storage": "~0.2.0", "@dev-plugins/react-native-mmkv": "~0.2.0", "@dev-plugins/react-navigation": "~0.2.0", "@dev-plugins/react-query": "~0.2.0", "@dev-plugins/redux": "~0.2.0", "@dev-plugins/tinybase": "~0.2.0", "@dev-plugins/vanilla-log-viewer": "~0.2.0", "@expo/metro-runtime": "~4.0.1", "@expo/vector-icons": "~14.0.4", "@react-native-async-storage/async-storage": "1.23.1", "@react-native-community/datetimepicker": "8.2.0", "@react-native-masked-view/masked-view": "0.3.2", "@react-native-community/netinfo": "11.4.1", "@react-native-community/slider": "4.5.5", "@react-native-community/viewpager": "5.0.11", "@react-native-picker/picker": "2.9.0", "@react-native-segmented-control/segmented-control": "2.5.4", "@stripe/stripe-react-native": "0.38.6", "eslint-config-expo": "~8.0.1", "expo-analytics-amplitude": "~11.3.0", "expo-app-auth": "~11.1.0", "expo-app-loader-provider": "~8.0.0", "expo-apple-authentication": "~7.1.3", "expo-application": "~6.0.2", "expo-asset": "~11.0.5", "expo-audio": "~0.3.5", "expo-auth-session": "~6.0.3", "expo-av": "~15.0.2", "expo-background-fetch": "~13.0.6", "expo-background-task": "~0.1.4", "expo-battery": "~9.0.2", "expo-blur": "~14.0.3", "expo-brightness": "~13.0.3", "expo-build-properties": "~0.13.3", "expo-calendar": "~14.0.6", "expo-camera": "~16.0.18", "expo-cellular": "~7.0.2", "expo-checkbox": "~4.0.1", "expo-clipboard": "~7.0.1", "expo-constants": "~17.0.8", "expo-contacts": "~14.0.5", "expo-crypto": "~14.0.2", "expo-dev-client": "~5.0.20", "expo-device": "~7.0.3", "expo-document-picker": "~13.0.3", "expo-face-detector": "~13.0.2", "expo-file-system": "~18.0.12", "expo-font": "~13.0.4", "expo-gl": "~15.0.5", "expo-google-app-auth": "~8.3.0", "expo-haptics": "~14.0.1", "expo-image": "~2.0.7", "expo-image-loader": "~5.0.0", "expo-image-manipulator": "~13.0.6", "expo-image-picker": "~16.0.6", "expo-intent-launcher": "~12.0.2", "expo-insights": "~0.8.2", "expo-keep-awake": "~14.0.3", "expo-linear-gradient": "~14.0.2", "expo-linking": "~7.0.5", "expo-local-authentication": "~15.0.2", "expo-localization": "~16.0.1", "expo-location": "~18.0.10", "expo-mail-composer": "~14.0.2", "expo-media-library": "~17.0.6", "expo-module-template": "~10.15.14", "expo-modules-core": "~2.2.3", "expo-navigation-bar": "~4.0.9", "expo-network": "~7.0.5", "expo-notifications": "~0.29.14", "expo-print": "~14.0.3", "expo-router": "~4.0.21", "expo-screen-capture": "~7.0.1", "expo-screen-orientation": "~8.0.4", "expo-secure-store": "~14.0.1", "expo-sensors": "~14.0.2", "expo-sharing": "~13.0.1", "expo-sms": "~13.0.1", "expo-speech": "~13.0.1", "expo-splash-screen": "~0.29.24", "expo-sqlite": "~15.1.4", "expo-status-bar": "~2.0.1", "expo-store-review": "~8.0.1", "expo-symbols": "~0.2.2", "expo-system-ui": "~4.0.9", "expo-task-manager": "~12.0.6", "expo-tracking-transparency": "~5.1.1", "expo-updates": "~0.27.4", "expo-video-thumbnails": "~9.0.3", "expo-video": "~2.0.6", "expo-web-browser": "~14.0.2", "jest-expo": "~52.0.6", "lottie-react-native": "7.1.0", "react": "18.3.1", "react-dom": "18.3.1", "react-native": "0.76.9", "react-native-web": "~0.19.13", "react-native-gesture-handler": "~2.20.2", "react-native-get-random-values": "~1.11.0", "react-native-maps": "1.18.0", "react-native-pager-view": "6.5.1", "react-native-reanimated": "~3.16.1", "react-native-screens": "~4.4.0", "react-native-safe-area-context": "4.12.0", "react-native-svg": "15.8.0", "react-native-view-shot": "~4.0.3", "react-native-webview": "13.12.5", "sentry-expo": "~7.0.0", "unimodules-app-loader": "~5.0.1", "unimodules-image-loader-interface": "~6.1.0", "@shopify/react-native-skia": "1.5.0", "@shopify/flash-list": "1.7.3", "@sentry/react-native": "~6.10.0"}