{"version": 3, "file": "resolveAssetSource.js", "sourceRoot": "", "sources": ["../src/resolveAssetSource.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,YAAY,EAAE,MAAM,wCAAwC,CAAC;AAEtE,OAAO,mBAA4C,MAAM,uBAAuB,CAAC;AAEjF,IAAI,wBAAgF,CAAC;AAErF,MAAM,UAAU,0BAA0B,CACxC,WAAmE;IAEnE,wBAAwB,GAAG,WAAW,CAAC;AACzC,CAAC;AAED;;;GAGG;AACH,MAAM,CAAC,OAAO,UAAU,kBAAkB,CAAC,MAAW;IACpD,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE;QAC9B,OAAO,MAAM,CAAC;KACf;IAED,MAAM,KAAK,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC;IACnC,IAAI,CAAC,KAAK,EAAE;QACV,OAAO,IAAI,CAAC;KACb;IAED,MAAM,QAAQ,GAAG,IAAI,mBAAmB;IACtC,8CAA8C;IAC9C,kBAAkB,EAClB,IAAI,EACJ,KAAK,CACN,CAAC;IACF,IAAI,wBAAwB,EAAE;QAC5B,OAAO,wBAAwB,CAAC,QAAQ,CAAC,CAAC;KAC3C;IACD,OAAO,QAAQ,CAAC,YAAY,EAAE,CAAC;AACjC,CAAC;AAED,MAAM,CAAC,cAAc,CAAC,kBAAkB,EAAE,4BAA4B,EAAE;IACtE,GAAG;QACD,OAAO,0BAA0B,CAAC;IACpC,CAAC;CACF,CAAC,CAAC;AAEH,MAAM,CAAC,MAAM,EAAE,SAAS,EAAE,GAAG,mBAAmB,CAAC", "sourcesContent": ["import { getAssetByID } from '@react-native/assets-registry/registry';\n\nimport AssetSourceResolver, { ResolvedAssetSource } from './AssetSourceResolver';\n\nlet _customSourceTransformer: (resolver: AssetSourceResolver) => ResolvedAssetSource;\n\nexport function setCustomSourceTransformer(\n  transformer: (resolver: AssetSourceResolver) => ResolvedAssetSource\n): void {\n  _customSourceTransformer = transformer;\n}\n\n/**\n * `source` is either a number (opaque type returned by require('./foo.png'))\n * or an `ImageSource` like { uri: '<http location || file path>' }\n */\nexport default function resolveAssetSource(source: any): ResolvedAssetSource | null {\n  if (typeof source === 'object') {\n    return source;\n  }\n\n  const asset = getAssetByID(source);\n  if (!asset) {\n    return null;\n  }\n\n  const resolver = new AssetSourceResolver(\n    // Doesn't matter since this is removed on web\n    'https://expo.dev',\n    null,\n    asset\n  );\n  if (_customSourceTransformer) {\n    return _customSourceTransformer(resolver);\n  }\n  return resolver.defaultAsset();\n}\n\nObject.defineProperty(resolveAssetSource, 'setCustomSourceTransformer', {\n  get() {\n    return setCustomSourceTransformer;\n  },\n});\n\nexport const { pickScale } = AssetSourceResolver;\n"]}