{"version": 3, "file": "ExpoModuleConfig.js", "sourceRoot": "", "sources": ["../src/ExpoModuleConfig.ts"], "names": [], "mappings": ";;;AAQA,SAAS,QAAQ,CAAI,KAA0B;IAC7C,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;QACxB,OAAO,KAAK,CAAC;KACd;IACD,OAAO,KAAK,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;AACtC,CAAC;AAED;;GAEG;AACH,MAAa,gBAAgB;IACN;IAArB,YAAqB,SAA8B;QAA9B,cAAS,GAAT,SAAS,CAAqB;IAAG,CAAC;IAEvD;;OAEG;IACH,gBAAgB,CAAC,QAA2B;QAC1C,MAAM,kBAAkB,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,IAAI,EAAE,CAAC;QAE1D,IAAI,QAAQ,KAAK,OAAO,EAAE;YACxB,4EAA4E;YAC5E,OAAO,kBAAkB,CAAC,IAAI,CAAC,CAAC,iBAAiB,EAAE,EAAE;gBACnD,OAAO,CAAC,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC,QAAQ,CAAC,iBAAiB,CAAC,CAAC;YACvE,CAAC,CAAC,CAAC;SACJ;QACD,OAAO,kBAAkB,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;IAC/C,CAAC;IAED;;OAEG;IACH,cAAc;QACZ,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,IAAI,IAAI,CAAC,SAAS,CAAC,GAAG,IAAI,IAAI,CAAC;IAC5D,CAAC;IAED;;OAEG;IACH,YAAY;QACV,MAAM,WAAW,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;QAE1C,4DAA4D;QAC5D,OAAO,WAAW,EAAE,OAAO,IAAI,WAAW,EAAE,iBAAiB,IAAI,EAAE,CAAC;IACtE,CAAC;IAED;;OAEG;IACH,2BAA2B;QACzB,OAAO,IAAI,CAAC,cAAc,EAAE,EAAE,sBAAsB,IAAI,EAAE,CAAC;IAC7D,CAAC;IAED;;OAEG;IACH,0BAA0B;QACxB,OAAO,IAAI,CAAC,cAAc,EAAE,EAAE,qBAAqB,IAAI,EAAE,CAAC;IAC5D,CAAC;IAED;;OAEG;IACH,iBAAiB;QACf,OAAO,QAAQ,CAAC,IAAI,CAAC,cAAc,EAAE,EAAE,WAAW,CAAC,CAAC;IACtD,CAAC;IAED;;OAEG;IACH,qBAAqB;QACnB,OAAO,QAAQ,CAAC,IAAI,CAAC,cAAc,EAAE,EAAE,eAAe,CAAC,CAAC;IAC1D,CAAC;IAED;;OAEG;IACH,cAAc;QACZ,OAAO,IAAI,CAAC,cAAc,EAAE,EAAE,SAAS,IAAI,KAAK,CAAC;IACnD,CAAC;IAED;;OAEG;IACH,cAAc;QACZ,MAAM,aAAa,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;QAE7C,4DAA4D;QAC5D,OAAO,aAAa,EAAE,OAAO,IAAI,aAAa,EAAE,iBAAiB,IAAI,EAAE,CAAC;IAC1E,CAAC;IAED;;OAEG;IACH,kBAAkB;QAChB,OAAO,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,UAAU,IAAI,EAAE,CAAC,CAAC;IAC5D,CAAC;IAED;;OAEG;IACH,oBAAoB;QAClB,OAAO,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,aAAa,IAAI,EAAE,CAAC,CAAC;IAC/D,CAAC;IAED;;OAEG;IACH,wBAAwB;QACtB,OAAO,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,iBAAiB,IAAI,EAAE,CAAC,CAAC;IACnE,CAAC;IAED;;OAEG;IACH,MAAM;QACJ,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;CACF;AA3GD,4CA2GC;AAED;;GAEG;AACH,SAAgB,iCAAiC,CAAC,IAAY;IAC5D,kDAAkD;IAClD,4DAA4D;IAC5D,OAAO,IAAI,gBAAgB,CAAC,OAAO,CAAC,IAAI,CAAwB,CAAC,CAAC;AACpE,CAAC;AAJD,8EAIC", "sourcesContent": ["import {\n  AndroidGradleAarProjectDescriptor,\n  AndroidGradlePluginDescriptor,\n  RawExpoModuleConfig,\n  RawModuleConfigApple,\n  SupportedPlatform,\n} from './types';\n\nfunction arrayize<T>(value: T[] | T | undefined): T[] {\n  if (Array.isArray(value)) {\n    return value;\n  }\n  return value != null ? [value] : [];\n}\n\n/**\n * A class that wraps the raw config (`expo-module.json` or `unimodule.json`).\n */\nexport class ExpoModuleConfig {\n  constructor(readonly rawConfig: RawExpoModuleConfig) {}\n\n  /**\n   * Whether the module supports given platform.\n   */\n  supportsPlatform(platform: SupportedPlatform): boolean {\n    const supportedPlatforms = this.rawConfig.platforms ?? [];\n\n    if (platform === 'apple') {\n      // Apple platform is supported when any of iOS, macOS and tvOS is supported.\n      return supportedPlatforms.some((supportedPlatform) => {\n        return ['apple', 'ios', 'macos', 'tvos'].includes(supportedPlatform);\n      });\n    }\n    return supportedPlatforms.includes(platform);\n  }\n\n  /**\n   * Returns the generic config for all Apple platforms with a fallback to the legacy iOS config.\n   */\n  getAppleConfig(): RawModuleConfigApple | null {\n    return this.rawConfig.apple ?? this.rawConfig.ios ?? null;\n  }\n\n  /**\n   * Returns a list of names of Swift native modules classes to put to the generated modules provider file.\n   */\n  appleModules() {\n    const appleConfig = this.getAppleConfig();\n\n    // `modulesClassNames` is a legacy name for the same config.\n    return appleConfig?.modules ?? appleConfig?.modulesClassNames ?? [];\n  }\n\n  /**\n   * Returns a list of names of Swift classes that receives AppDelegate life-cycle events.\n   */\n  appleAppDelegateSubscribers(): string[] {\n    return this.getAppleConfig()?.appDelegateSubscribers ?? [];\n  }\n\n  /**\n   * Returns a list of names of Swift classes that implement `ExpoReactDelegateHandler`.\n   */\n  appleReactDelegateHandlers(): string[] {\n    return this.getAppleConfig()?.reactDelegateHandlers ?? [];\n  }\n\n  /**\n   * Returns podspec paths defined by the module author.\n   */\n  applePodspecPaths(): string[] {\n    return arrayize(this.getAppleConfig()?.podspecPath);\n  }\n\n  /**\n   * Returns the product module names, if defined by the module author.\n   */\n  appleSwiftModuleNames(): string[] {\n    return arrayize(this.getAppleConfig()?.swiftModuleName);\n  }\n\n  /**\n   * Returns whether this module will be added only to the debug configuration\n   */\n  appleDebugOnly(): boolean {\n    return this.getAppleConfig()?.debugOnly ?? false;\n  }\n\n  /**\n   * Returns a list of names of Kotlin native modules classes to put to the generated package provider file.\n   */\n  androidModules() {\n    const androidConfig = this.rawConfig.android;\n\n    // `modulesClassNames` is a legacy name for the same config.\n    return androidConfig?.modules ?? androidConfig?.modulesClassNames ?? [];\n  }\n\n  /**\n   * Returns build.gradle file paths defined by the module author.\n   */\n  androidGradlePaths(): string[] {\n    return arrayize(this.rawConfig.android?.gradlePath ?? []);\n  }\n\n  /**\n   * Returns gradle plugins descriptors defined by the module author.\n   */\n  androidGradlePlugins(): AndroidGradlePluginDescriptor[] {\n    return arrayize(this.rawConfig.android?.gradlePlugins ?? []);\n  }\n\n  /**\n   * Returns gradle projects containing AAR files defined by the module author.\n   */\n  androidGradleAarProjects(): AndroidGradleAarProjectDescriptor[] {\n    return arrayize(this.rawConfig.android?.gradleAarProjects ?? []);\n  }\n\n  /**\n   * Returns serializable raw config.\n   */\n  toJSON(): RawExpoModuleConfig {\n    return this.rawConfig;\n  }\n}\n\n/**\n * Reads the config at given path and returns the config wrapped by `ExpoModuleConfig` class.\n */\nexport function requireAndResolveExpoModuleConfig(path: string): ExpoModuleConfig {\n  // TODO: Validate the raw config against a schema.\n  // TODO: Support for `*.js` files, not only static `*.json`.\n  return new ExpoModuleConfig(require(path) as RawExpoModuleConfig);\n}\n"]}