{"version": 3, "file": "types.js", "sourceRoot": "", "sources": ["../src/types.ts"], "names": [], "mappings": "", "sourcesContent": ["import { ExpoModuleConfig } from './ExpoModuleConfig';\n\nexport type SupportedPlatform = 'apple' | 'ios' | 'android' | 'web' | 'macos' | 'tvos' | 'devtools';\n\n/**\n * Options that can be passed through `expo.autolinking` config in the package.json file.\n */\nexport type AutolinkingOptions = {\n  searchPaths?: string[] | null;\n  ignorePaths?: string[] | null;\n  exclude?: string[] | null;\n  flags?: Record<string, any>;\n} & {\n  [key in SupportedPlatform]?: AutolinkingOptions;\n};\n\nexport interface SearchOptions {\n  // Available in the CLI\n  searchPaths: string[];\n  ignorePaths?: string[] | null;\n  exclude?: string[] | null;\n  platform: SupportedPlatform;\n  silent?: boolean;\n  nativeModulesDir?: string | null;\n  projectRoot: string;\n  /**\n   * Filter the search results to only include the project dependencies.\n   * In a monorepo, you may like to set this to false and link all modules from the monorepo.\n   * @default true\n   */\n  onlyProjectDeps?: boolean;\n\n  // Scratched from project's config\n  flags?: Record<string, any>;\n}\n\nexport interface ResolveOptions extends SearchOptions {\n  json?: boolean;\n}\n\nexport interface GenerateOptions extends ResolveOptions {\n  target: string;\n  namespace?: string;\n  empty?: boolean;\n}\n\nexport interface GenerateModulesProviderOptions extends ResolveOptions {\n  target: string;\n  entitlement?: string;\n  packages: string[];\n}\n\nexport type PackageRevision = {\n  path: string;\n  version: string;\n  config?: ExpoModuleConfig;\n  duplicates?: PackageRevision[];\n};\n\nexport type SearchResults = {\n  [moduleName: string]: PackageRevision;\n};\n\nexport interface ModuleAndroidProjectInfo {\n  name: string;\n  sourceDir: string;\n}\n\nexport interface ModuleAndroidPluginInfo {\n  id: string;\n  sourceDir: string;\n}\n\nexport interface ModuleAndroidAarProjectInfo extends AndroidGradleAarProjectDescriptor {\n  projectDir: string;\n}\n\nexport interface ModuleDescriptorAndroid {\n  packageName: string;\n  projects: ModuleAndroidProjectInfo[];\n  plugins?: ModuleAndroidPluginInfo[];\n  modules: string[];\n  aarProjects?: ModuleAndroidAarProjectInfo[];\n}\n\nexport interface ModuleIosPodspecInfo {\n  podName: string;\n  podspecDir: string;\n}\nexport interface ModuleDescriptorIos {\n  packageName: string;\n  pods: ModuleIosPodspecInfo[];\n  flags: Record<string, any> | undefined;\n  swiftModuleNames: string[];\n  modules: string[];\n  appDelegateSubscribers: string[];\n  reactDelegateHandlers: string[];\n  debugOnly: boolean;\n}\n\nexport interface ModuleDescriptorDevTools {\n  packageName: string;\n  packageRoot: string;\n  webpageRoot: string;\n}\n\nexport type ModuleDescriptor =\n  | ModuleDescriptorAndroid\n  | ModuleDescriptorIos\n  | ModuleDescriptorDevTools;\n\nexport interface AndroidGradlePluginDescriptor {\n  /**\n   * Gradle plugin ID\n   */\n  id: string;\n\n  /**\n   * Artifact group\n   */\n  group: string;\n\n  /**\n   * Relative path to the gradle plugin directory\n   */\n  sourceDir: string;\n}\n\nexport interface AndroidGradleAarProjectDescriptor {\n  /**\n   * Gradle project name\n   */\n  name: string;\n\n  /**\n   * Path to the AAR file\n   */\n  aarFilePath: string;\n}\n\n/**\n * Represents a raw config specific to Apple platforms.\n */\nexport type RawModuleConfigApple = {\n  /**\n   * Names of Swift native modules classes to put to the generated modules provider file.\n   */\n  modules?: string[];\n\n  /**\n   * Names of Swift native modules classes to put to the generated modules provider file.\n   * @deprecated Deprecated in favor of `modules`. Might be removed in the future releases.\n   */\n  modulesClassNames?: string[];\n\n  /**\n   * Names of Swift classes that hooks into `ExpoAppDelegate` to receive AppDelegate life-cycle events.\n   */\n  appDelegateSubscribers?: string[];\n\n  /**\n   * Names of Swift classes that implement `ExpoReactDelegateHandler` to hook React instance creation.\n   */\n  reactDelegateHandlers?: string[];\n\n  /**\n   * Podspec relative path.\n   * To have multiple podspecs, string array type is also supported.\n   */\n  podspecPath?: string | string[];\n\n  /**\n   * Swift product module name. If empty, the pod name is used for Swift imports.\n   * To have multiple modules, string array is also supported.\n   */\n  swiftModuleName?: string | string[];\n\n  /**\n   * Whether this module will be added only to the debug configuration.\n   * Defaults to false.\n   */\n  debugOnly?: boolean;\n};\n\n/**\n * Represents a raw config from `expo-module.json`.\n */\nexport interface RawExpoModuleConfig {\n  /**\n   * An array of supported platforms.\n   */\n  platforms?: SupportedPlatform[];\n\n  /**\n   * A config for all Apple platforms.\n   */\n  apple?: RawModuleConfigApple;\n\n  /**\n   * The legacy config previously used for iOS platform. For backwards compatibility it's used as the fallback for `apple`.\n   * Also due to backwards compatibility, it includes the deprecated `modulesClassNames` field.\n   * @deprecated As the module can now support more than iOS platform, use the generic `apple` config instead.\n   */\n  ios?: RawModuleConfigApple;\n\n  /**\n   * Android-specific config.\n   */\n  android?: {\n    /**\n     * Full names (package + class name) of Kotlin native modules classes to put to the generated package provider file.\n     */\n    modules?: string[];\n\n    /**\n     * Full names (package + class name) of Kotlin native modules classes to put to the generated package provider file.\n     * @deprecated Deprecated in favor of `modules`. Might be removed in the future releases.\n     */\n    modulesClassNames?: string[];\n\n    /**\n     * build.gradle relative path.\n     * To have multiple build.gradle projects, string array type is also supported.\n     */\n    gradlePath?: string | string[];\n\n    /**\n     * Gradle plugins.\n     */\n    gradlePlugins?: AndroidGradlePluginDescriptor[];\n\n    /**\n     * Gradle projects containing AAR files.\n     */\n    gradleAarProjects?: AndroidGradleAarProjectDescriptor[];\n  };\n\n  /**\n   * DevTools-specific config.\n   */\n  devtools?: {\n    /**\n     * The webpage root directory for Expo CLI DevTools to serve the web resources.\n     */\n    webpageRoot: string;\n  };\n}\n\ninterface AndroidMavenRepositoryPasswordCredentials {\n  username: string;\n  password: string;\n}\n\ninterface AndroidMavenRepositoryHttpHeaderCredentials {\n  name: string;\n  value: string;\n}\n\ninterface AndroidMavenRepositoryAWSCredentials {\n  accessKey: string;\n  secretKey: string;\n  sessionToken?: string;\n}\n\ntype AndroidMavenRepositoryCredentials =\n  | AndroidMavenRepositoryPasswordCredentials\n  | AndroidMavenRepositoryHttpHeaderCredentials\n  | AndroidMavenRepositoryAWSCredentials;\n\nexport interface AndroidMavenRepository {\n  /**\n   * The URL of the Maven repository.\n   */\n  url: string;\n  /**\n   * The credentials to use when accessing the Maven repository.\n   * May be of type PasswordCredentials, HttpHeaderCredentials, or AWSCredentials.\n   *\n   * @see the authentication schemes section of [Gradle documentation](https://docs.gradle.org/current/userguide/declaring_repositories.html#sec:authentication_schemes) for more information.\n   */\n  credentials?: AndroidMavenRepositoryCredentials;\n  /**\n   * The authentication scheme to use when accessing the Maven repository.\n   */\n  authentication?: 'basic' | 'digest' | 'header';\n}\n\ninterface ApplePod {\n  name: string;\n  version?: string;\n  configurations?: string[];\n  modular_headers?: boolean;\n  source?: string;\n  path?: string;\n  podspec?: string;\n  testspecs?: string[];\n  git?: string;\n  branch?: string;\n  tag?: string;\n  commit?: string;\n}\n\nexport type ExtraDependencies = AndroidMavenRepository[] | ApplePod[];\n\n/**\n * Represents code signing entitlements passed to the `ExpoModulesProvider` for Apple platforms.\n */\nexport interface AppleCodeSignEntitlements {\n  /**\n   * @see https://developer.apple.com/documentation/bundleresources/entitlements/com_apple_security_application-groups\n   */\n  appGroups?: string[];\n}\n"]}