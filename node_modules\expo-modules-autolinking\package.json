{"name": "expo-modules-autolinking", "version": "2.0.8", "description": "Scripts that autolink Expo modules.", "main": "build/index.js", "types": "build/index.d.ts", "scripts": {"build": "expo-module build", "clean": "expo-module clean", "lint": "expo-module lint", "test": "expo-module test", "prepare": "expo-module prepare", "prepublishOnly": "expo-module prepublishOnly", "expo-module": "expo-module"}, "bin": {"expo-modules-autolinking": "bin/expo-modules-autolinking.js"}, "keywords": ["expo", "expo-module", "autolinking", "unimodules"], "repository": {"type": "git", "url": "https://github.com/expo/expo.git", "directory": "packages/expo-modules-autolinking"}, "bugs": {"url": "https://github.com/expo/expo/issues"}, "author": "650 Industries, Inc.", "license": "MIT", "homepage": "https://github.com/expo/expo/tree/main/packages/expo-modules-autolinking#readme", "devDependencies": {"@types/fs-extra": "^9.0.11", "expo-module-scripts": "~4.0.4", "minimatch": "^3.0.4"}, "dependencies": {"@expo/spawn-async": "^1.7.2", "chalk": "^4.1.0", "commander": "^7.2.0", "fast-glob": "^3.2.5", "find-up": "^5.0.0", "fs-extra": "^9.1.0", "require-from-string": "^2.0.2", "resolve-from": "^5.0.0"}, "gitHead": "c01c449a1d6e6e8690bfcc88a778b46781a59674"}