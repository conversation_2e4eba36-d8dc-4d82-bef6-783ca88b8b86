{"version": 3, "file": "Cache.js", "sourceRoot": "", "sources": ["../src/Cache.ts"], "names": [], "mappings": ";;;;;;AAAA,oDAA4B;AAC5B,uCAA6F;AAC7F,+BAAqC;AAIrC,MAAM,cAAc,GAAG,mCAAmC,CAAC;AAE3D,MAAM,SAAS,GAA8B,EAAE,CAAC;AAEhD,kEAAkE;AAClE,SAAS,aAAa,CAAC,QAAgB;IACrC,MAAM,QAAQ,GAAG,QAAQ,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAA,uBAAY,EAAC,QAAQ,CAAC,CAAC;IACjF,OAAO,gBAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AACpE,CAAC;AAED,0DAA0D;AAC1D,SAAgB,cAAc,CAAC,UAAkB,EAAE,UAAoB;IACrE,MAAM,IAAI,GAAG,aAAa,CAAC,UAAU,CAAC,CAAC;IACvC,OAAO,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAC7D,CAAC;AAHD,wCAGC;AAEM,KAAK,UAAU,gCAAgC,CACpD,WAAmB,EACnB,IAAY,EACZ,IAAkB;IAElB,MAAM,cAAc,GAAa,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;IAEnD,IAAI,IAAI,CAAC,eAAe,EAAE;QACxB,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;KAC3C;IAED,MAAM,QAAQ,GAAG,GAAG,IAAI,IAAI,cAAc,CAAC,IAAI,CAAC,GAAG,EAAE,cAAc,CAAC,EAAE,CAAC;IACvE,IAAI,CAAC,CAAC,QAAQ,IAAI,SAAS,CAAC,EAAE;QAC5B,SAAS,CAAC,QAAQ,CAAC,GAAG,MAAM,oBAAoB,CAAC,WAAW,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC;KAC/E;IAED,OAAO,QAAQ,CAAC;AAClB,CAAC;AAjBD,4EAiBC;AAEM,KAAK,UAAU,oBAAoB,CACxC,WAAmB,EACnB,IAAY,EACZ,QAAgB;IAEhB,MAAM,WAAW,GAAG,IAAA,WAAI,EAAC,WAAW,EAAE,cAAc,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC;IACtE,MAAM,IAAA,oBAAS,EAAC,WAAW,CAAC,CAAC;IAC7B,OAAO,WAAW,CAAC;AACrB,CAAC;AARD,oDAQC;AAEM,KAAK,UAAU,sBAAsB,CAC1C,QAAgB,EAChB,QAAgB;IAEhB,IAAI;QACF,OAAO,MAAM,IAAA,mBAAQ,EAAC,IAAA,cAAO,EAAC,SAAS,CAAC,QAAQ,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC;KAC/D;IAAC,MAAM;QACN,OAAO,IAAI,CAAC;KACb;AACH,CAAC;AATD,wDASC;AAEM,KAAK,UAAU,eAAe,CACnC,QAAgB,EAChB,MAAc,EACd,QAAgB;IAEhB,IAAI;QACF,MAAM,IAAA,oBAAS,EAAC,IAAA,cAAO,EAAC,SAAS,CAAC,QAAQ,CAAC,EAAE,QAAQ,CAAC,EAAE,MAAM,CAAC,CAAC;KACjE;IAAC,OAAO,KAAU,EAAE;QACnB,OAAO,CAAC,IAAI,CAAC,yBAAyB,QAAQ,MAAM,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;KACtE;AACH,CAAC;AAVD,0CAUC;AAEM,KAAK,UAAU,sBAAsB,CAAC,WAAmB,EAAE,IAAY;IAC5E,0BAA0B;IAC1B,MAAM,WAAW,GAAG,IAAA,WAAI,EAAC,WAAW,EAAE,cAAc,EAAE,IAAI,CAAC,CAAC;IAC5D,MAAM,IAAA,oBAAS,EAAC,WAAW,CAAC,CAAC;IAC7B,MAAM,aAAa,GAAG,IAAA,sBAAW,EAAC,WAAW,CAAC,CAAC;IAE/C,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,aAAa,CAAC,EAAE;QACjC,OAAO,CAAC,IAAI,CAAC,+BAA+B,CAAC,CAAC;QAC9C,OAAO;KACR;IACD,MAAM,mBAAmB,GAAoB,EAAE,CAAC;IAChD,KAAK,MAAM,KAAK,IAAI,aAAa,EAAE;QACjC,sBAAsB;QACtB,IAAI,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE;YACzB,SAAS;SACV;QAED,SAAS;QACT,IAAI,CAAC,CAAC,KAAK,IAAI,SAAS,CAAC,EAAE;YACzB,mBAAmB,CAAC,IAAI,CAAC,IAAA,iBAAM,EAAC,IAAA,WAAI,EAAC,WAAW,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC;SAC5D;KACF;IAED,MAAM,OAAO,CAAC,GAAG,CAAC,mBAAmB,CAAC,CAAC;AACzC,CAAC;AAxBD,wDAwBC"}