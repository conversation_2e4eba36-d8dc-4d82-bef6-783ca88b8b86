{"version": 3, "file": "fileUtils.js", "sourceRoot": "", "sources": ["../src/fileUtils.ts"], "names": [], "mappings": ";;;;;;AAAA,0DAA6B;AAC7B,2DAA6B;AAC7B,gDAAwB;AASxB;;GAEG;AACI,KAAK,UAAU,eAAe,CAAC,IAAY;IAChD,OAAO,CAAC,MAAM,kBAAE,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,CAAC,EAAE,MAAM,EAAE,IAAI,KAAK,CAAC;AACpE,CAAC;AAFD,0CAEC;AAED;;GAEG;AACI,KAAK,UAAU,wBAAwB,CAC5C,WAAmB,EACnB,YAA0B,EAC1B,OAAqB;IAErB,MAAM,UAAU,GAAG,mBAAI,CAAC,MAAM,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;IACrD,MAAM,GAAG,GAAG,OAAO,EAAE,GAAG,IAAI,OAAO,CAAC,GAAG,EAAE,CAAC;IAC1C,MAAM,OAAO,GAAa,EAAE,CAAC;IAC7B,IAAI,KAAK,EAAE,MAAM,IAAI,IAAI,UAAU,EAAE;QACnC,IAAI,QAAQ,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAC/B,IAAI,CAAC,cAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE;YAC9B,QAAQ,GAAG,cAAI,CAAC,OAAO,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;SACxC;QACD,MAAM,QAAQ,GAAG,MAAM,kBAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;QAC7C,MAAM,OAAO,GAAG,YAAY,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;QACjD,IAAI,OAAO,IAAI,IAAI,EAAE;YACnB,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;SACvB;KACF;IACD,OAAO,OAAO,CAAC;AACjB,CAAC;AApBD,4DAoBC;AAED;;GAEG;AACI,KAAK,UAAU,0BAA0B,CAC9C,WAAmB,EACnB,YAA0B,EAC1B,OAAqB;IAErB,MAAM,UAAU,GAAG,mBAAI,CAAC,MAAM,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;IACrD,MAAM,GAAG,GAAG,OAAO,EAAE,GAAG,IAAI,OAAO,CAAC,GAAG,EAAE,CAAC;IAC1C,IAAI,KAAK,EAAE,MAAM,IAAI,IAAI,UAAU,EAAE;QACnC,IAAI,QAAQ,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAC/B,IAAI,CAAC,cAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE;YAC9B,QAAQ,GAAG,cAAI,CAAC,OAAO,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;SACxC;QACD,MAAM,QAAQ,GAAG,MAAM,kBAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;QAC7C,MAAM,OAAO,GAAG,YAAY,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;QACjD,IAAI,OAAO,IAAI,IAAI,EAAE;YACnB,OAAO,OAAO,CAAC;SAChB;KACF;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAnBD,gEAmBC", "sourcesContent": ["import glob from 'fast-glob';\nimport fs from 'fs/promises';\nimport path from 'path';\n\ntype GlobOptions = Parameters<typeof glob>[1];\n\n/**\n * A matching function that takes a file path and its contents and returns a string if it matches, or null otherwise.\n */\ntype MatchFunctor = (filePath: string, contents: Buffer) => string | null;\n\n/**\n * Check if the file exists.\n */\nexport async function fileExistsAsync(file: string): Promise<boolean> {\n  return (await fs.stat(file).catch(() => null))?.isFile() ?? false;\n}\n\n/**\n * Search files that match the glob pattern and return all matches from the matchFunctor.\n */\nexport async function globMatchFunctorAllAsync(\n  globPattern: string,\n  matchFunctor: MatchFunctor,\n  options?: GlobOptions\n): Promise<string[]> {\n  const globStream = glob.stream(globPattern, options);\n  const cwd = options?.cwd ?? process.cwd();\n  const results: string[] = [];\n  for await (const file of globStream) {\n    let filePath = file.toString();\n    if (!path.isAbsolute(filePath)) {\n      filePath = path.resolve(cwd, filePath);\n    }\n    const contents = await fs.readFile(filePath);\n    const matched = matchFunctor(filePath, contents);\n    if (matched != null) {\n      results.push(matched);\n    }\n  }\n  return results;\n}\n\n/**\n * Search files that match the glob pattern and return the first match from the matchFunctor.\n */\nexport async function globMatchFunctorFirstAsync(\n  globPattern: string,\n  matchFunctor: MatchFunctor,\n  options?: GlobOptions\n): Promise<string | null> {\n  const globStream = glob.stream(globPattern, options);\n  const cwd = options?.cwd ?? process.cwd();\n  for await (const file of globStream) {\n    let filePath = file.toString();\n    if (!path.isAbsolute(filePath)) {\n      filePath = path.resolve(cwd, filePath);\n    }\n    const contents = await fs.readFile(filePath);\n    const matched = matchFunctor(filePath, contents);\n    if (matched != null) {\n      return matched;\n    }\n  }\n  return null;\n}\n"]}