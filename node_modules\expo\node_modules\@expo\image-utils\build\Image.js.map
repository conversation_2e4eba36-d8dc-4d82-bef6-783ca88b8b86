{"version": 3, "file": "Image.js", "sourceRoot": "", "sources": ["../src/Image.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,kDAA0B;AAC1B,4CAAoB;AACpB,0DAAiC;AACjC,gDAAwB;AAExB,+CAAiC;AACjC,qDAAuC;AACvC,2CAA6B;AAE7B,+BAA4B;AAC5B,6CAA+B;AAC/B,+CAAiC;AAEjC,IAAI,SAAS,GAAY,KAAK,CAAC;AAE/B,KAAK,UAAU,iBAAiB,CAAC,MAAc,EAAE,KAAe;IAC9D,MAAM,KAAK,GAAG,MAAM,aAAa,EAAE,CAAC;IACpC,IAAI,CAAC,KAAK,EAAE;QACV,OAAO,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;KAC9C;IACD,OAAO,KAAK,CAAC,iBAAiB,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;AAChD,CAAC;AAED,KAAK,UAAU,WAAW,CAAC,YAA0B;IACnD,MAAM,KAAK,GAAQ,MAAM,aAAa,EAAE,CAAC;IACzC,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,eAAe,EAAE,UAAU,EAAE,GAAG,YAAY,CAAC;IACpE,IAAI,CAAC,KAAK,EAAE;QACV,MAAM,YAAY,GAAQ,EAAE,KAAK,EAAE,YAAY,CAAC,GAAG,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC;QACpE,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE;YAC3C,KAAK;YACL,MAAM;YACN,GAAG,EAAE,UAAU;YACf,UAAU,EAAE,eAAe;SAC5B,CAAC,CAAC;QAEH,IAAI,YAAY,CAAC,kBAAkB,EAAE;YACnC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;SACnB;QACD,IAAI,YAAY,CAAC,YAAY,EAAE;YAC7B,kGAAkG;YAClG,MAAM,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;SAC9B;QAED,wBAAwB;QACxB,OAAO,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;KACzC;IACD,IAAI;QACF,IAAI,WAAW,GAAG,KAAK,CAAC,YAAY,CAAC,GAAG,CAAC;aACtC,WAAW,EAAE;aACb,MAAM,CAAC,KAAK,EAAE,MAAM,EAAE,EAAE,GAAG,EAAE,UAAU,EAAE,UAAU,EAAE,aAAa,EAAE,CAAC,CAAC;QAEzE,kEAAkE;QAClE,IAAI,eAAe,IAAI,eAAe,KAAK,aAAa,EAAE;YACxD,wCAAwC;YACxC,WAAW,GAAG,WAAW,CAAC,SAAS,CAAC;gBAClC;oBACE,4BAA4B;oBAC5B,KAAK,EAAE;wBACL,MAAM,EAAE;4BACN,KAAK;4BACL,MAAM;4BACN,qBAAqB;4BACrB,QAAQ,EAAE,YAAY,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;4BACjD,UAAU,EAAE,eAAe;yBAC5B;qBACF;oBACD,gGAAgG;oBAChG,KAAK,EAAE,WAAW;iBACnB;aACF,CAAC,CAAC;SACJ;aAAM,IAAI,YAAY,CAAC,kBAAkB,EAAE;YAC1C,WAAW,CAAC,OAAO,EAAE,CAAC;SACvB;QAED,IAAI,YAAY,CAAC,YAAY,EAAE;YAC7B,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CACtB,iCAAiC,KAAK,aAAa,MAAM;cACnD,YAAY,CAAC,YAAY,SAAS,YAAY,CAAC,YAAY;gBAE/D,eAAe,IAAI,eAAe,KAAK,aAAa,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,MAC3E,YAAY,CACb,CAAC;YAEF,WAAW,CAAC,SAAS,CAAC,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC,CAAC,CAAC;SAC5D;QAED,OAAO,MAAM,WAAW,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE,CAAC;KAC3C;IAAC,OAAO,KAAU,EAAE;QACnB,MAAM,IAAI,KAAK,CACb,kDAAkD,YAAY,CAAC,GAAG,MAAM,KAAK,CAAC,OAAO,EAAE,CACxF,CAAC;KACH;AACH,CAAC;AAED,KAAK,UAAU,aAAa;IAC1B,IAAI,KAAU,CAAC;IACf,IAAI,MAAM,KAAK,CAAC,gBAAgB,EAAE;QAAE,KAAK,GAAG,MAAM,KAAK,CAAC,sBAAsB,EAAE,CAAC;IACjF,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAS,eAAe,CAAC,YAAoD;IAC3E,OAAO,YAAY,CAAC,KAAK,KAAK,YAAY,CAAC,MAAM;QAC/C,CAAC,CAAC,GAAG,YAAY,CAAC,KAAK,EAAE;QACzB,CAAC,CAAC,GAAG,YAAY,CAAC,KAAK,IAAI,YAAY,CAAC,MAAM,EAAE,CAAC;AACrD,CAAC;AAED,KAAK,UAAU,kCAAkC;IAC/C,IAAI,SAAG,CAAC,yBAAyB,EAAE;QACjC,OAAO;KACR;IAED,IAAI,SAAG,CAAC,sBAAsB,IAAI,CAAC,SAAS,IAAI,CAAC,CAAC,MAAM,KAAK,CAAC,gBAAgB,EAAE,CAAC,EAAE;QACjF,SAAS,GAAG,IAAI,CAAC;QACjB,OAAO,CAAC,IAAI,CACV,eAAK,CAAC,MAAM,CACV,qMAAqM,CACtM,CACF,CAAC;KACH;AACH,CAAC;AAED,MAAM,KAAK,GAA2B;IACpC,GAAG,EAAE,WAAW;IAChB,IAAI,EAAE,YAAY;IAClB,GAAG,EAAE,YAAY;IACjB,GAAG,EAAE,YAAY;IACjB,IAAI,EAAE,YAAY;IAClB,GAAG,EAAE,WAAW;CACjB,CAAC;AAEF,MAAM,gBAAgB,GAA2B;IAC/C,WAAW,EAAE,KAAK;IAClB,YAAY,EAAE,KAAK;IACnB,YAAY,EAAE,MAAM;IACpB,WAAW,EAAE,KAAK;CACnB,CAAC;AAEF,SAAgB,WAAW,CAAC,OAAe;IACzC,IAAI,OAAO,OAAO,KAAK,QAAQ;QAAE,OAAO,IAAI,CAAC;IAE7C,IAAI;QACF,yCAAyC;QACzC,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,OAAO,CAAC,CAAC;QAC7B,OAAO,GAAG,GAAG,CAAC,QAAQ,CAAC;KACxB;IAAC,MAAM,GAAE;IAEV,MAAM,GAAG,GAAG,cAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;IACrD,OAAO,KAAK,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC;AAC5B,CAAC;AAXD,kCAWC;AAED,KAAK,UAAU,uBAAuB,CAAC,YAA0B;IAC/D,MAAM,IAAI,GAAG;QACX,GAAG,YAAY;QACf,GAAG,EAAE,MAAM,QAAQ,CAAC,wBAAwB,CAAC,YAAY,CAAC,GAAG,CAAC;KAC/D,CAAC;IAEF,qBAAqB;IACrB,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;QACpB,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;KAC7B;IAED,MAAM,QAAQ,GAAG,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IACvC,IAAI,CAAC,QAAQ,EAAE;QACb,MAAM,IAAI,KAAK,CAAC,2CAA2C,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC;KACxE;IAED,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;QACd,IAAI,CAAC,IAAI,GAAG,QAAQ,eAAe,CAAC,YAAY,CAAC,IAAI,gBAAgB,CAAC,QAAQ,CAAC,EAAE,CAAC;KACnF;IAED,OAAO,IAAI,CAAC;AACd,CAAC;AAEM,KAAK,UAAU,4BAA4B,CAChD,YAAuC;IAEvC,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,eAAe,EAAE,YAAY,EAAE,GAAG,YAAY,CAAC;IACtE,MAAM,KAAK,GAAQ,MAAM,aAAa,EAAE,CAAC;IACzC,IAAI,CAAC,KAAK,EAAE;QACV,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC;YACxC,IAAI,EAAE,KAAK;YACX,KAAK,EAAE,eAAe;SACvB,CAAC,CAAC;QAEH,IAAI,YAAY,EAAE;YAChB,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;YACjD,kGAAkG;YAClG,OAAO,MAAM,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;SACtC;QACD,OAAO,IAAI,CAAC;KACb;IACD,MAAM,WAAW,GAAG,KAAK,CAAC;QACxB,MAAM,EAAE;YACN,KAAK;YACL,MAAM;YACN,QAAQ,EAAE,CAAC;YACX,UAAU,EAAE,eAAe;SAC5B;KACF,CAAC,CAAC;IAEH,IAAI,YAAY,CAAC,YAAY,EAAE;QAC7B,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CACtB,iCAAiC,KAAK,aAAa,MAAM;YACnD,YAAY,SAAS,YAAY;cAErC,eAAe,IAAI,eAAe,KAAK,aAAa,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,MAC3E,YAAY,CACb,CAAC;QAEF,WAAW,CAAC,SAAS,CAAC,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC,CAAC,CAAC;KAC5D;IAED,OAAO,MAAM,WAAW,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE,CAAC;AAC5C,CAAC;AAxCD,oEAwCC;AAEM,KAAK,UAAU,kBAAkB,CACtC,OAAoD,EACpD,YAA0B;IAE1B,MAAM,IAAI,GAAG,MAAM,uBAAuB,CAAC,YAAY,CAAC,CAAC;IAEzD,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE;QACtB,MAAM,kCAAkC,EAAE,CAAC;QAC3C,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,IAAK,EAAE,MAAM,EAAE,MAAM,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC;KAC9D;IAED,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,gCAAgC,CAC3D,OAAO,CAAC,WAAW,EACnB,OAAO,CAAC,SAAS,EACjB,IAAI,CACL,CAAC;IAEF,MAAM,IAAI,GAAG,IAAI,CAAC,IAAK,CAAC;IACxB,IAAI,MAAM,GAAkB,MAAM,KAAK,CAAC,sBAAsB,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;IAE/E,IAAI,CAAC,MAAM,EAAE;QACX,MAAM,kCAAkC,EAAE,CAAC;QAC3C,MAAM,GAAG,MAAM,WAAW,CAAC,IAAI,CAAC,CAAC;QACjC,MAAM,KAAK,CAAC,eAAe,CAAC,IAAI,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC;KACrD;IAED,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;AAC1B,CAAC;AA3BD,gDA2BC;AAEM,KAAK,UAAU,oBAAoB,CACxC,cAAsB,EACtB,QAAkB,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;IAE9B,MAAM,OAAO,GAAG,MAAM,iBAAiB,CAAC,cAAc,EAAE,KAAK,CAAC,CAAC;IAC/D,OAAO,MAAM,GAAG,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;AAC1C,CAAC;AAND,oDAMC;AAED;;;;;;;GAOG;AACI,KAAK,UAAU,oBAAoB,CAAC,EACzC,UAAU,EACV,UAAU,EACV,CAAC,GAAG,CAAC,EACL,CAAC,GAAG,CAAC,GAMN;IACC,MAAM,KAAK,GAAQ,MAAM,aAAa,EAAE,CAAC;IACzC,IAAI,CAAC,KAAK,EAAE;QACV,MAAM,KAAK,GAAG,CAAC,MAAM,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC,CAAC,SAAS,CAChE,MAAM,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,EACxC,CAAC,EACD,CAAC,CACF,CAAC;QACF,OAAO,MAAM,KAAK,CAAC,cAAc,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;KACpD;IACD,OAAO,MAAM,KAAK,CAAC,UAAU,CAAC;SAC3B,SAAS,CAAC,CAAC,EAAE,KAAK,EAAE,UAAU,EAAE,IAAI,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;SACnD,QAAQ,EAAE,CAAC;AAChB,CAAC;AAvBD,oDAuBC;AASM,KAAK,UAAU,UAAU,CAAC,GAAW;IAC1C,OAAO,MAAM,IAAA,mBAAQ,EAAC,YAAE,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC;AAC9C,CAAC;AAFD,gCAEC"}