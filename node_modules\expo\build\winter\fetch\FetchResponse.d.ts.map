{"version": 3, "file": "FetchResponse.d.ts", "sourceRoot": "", "sources": ["../../../src/winter/fetch/FetchResponse.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,cAAc,EAAE,MAAM,sBAAsB,CAAC;AAGtD,OAAO,KAAK,EAAE,cAAc,EAAE,MAAM,iBAAiB,CAAC;AAEtD,QAAA,MAAM,sBAAsB,uBAA0D,CAAC;AAEvF,MAAM,MAAM,gCAAgC,GAAG,MAAM,IAAI,CAAC;AAE1D;;GAEG;AACH,qBAAa,aAAc,SAAQ,sBAAuB,YAAW,QAAQ;IAI/D,OAAO,CAAC,QAAQ,CAAC,oBAAoB;IAHjD,OAAO,CAAC,cAAc,CAA4C;IAClE,OAAO,CAAC,UAAU,CAA2C;gBAEhC,oBAAoB,EAAE,gCAAgC;IAKnF,IAAI,IAAI,IAAI,cAAc,CAAC,UAAU,CAAC,GAAG,IAAI,CAuD5C;IAED,IAAI,OAAO,IAAI,OAAO,CAErB;IAED,IAAI,EAAE,IAAI,OAAO,CAEhB;IAED,SAAgB,IAAI,aAAa;IAE3B,IAAI,IAAI,OAAO,CAAC,IAAI,CAAC;IAKrB,QAAQ,IAAI,OAAO,CAAC,QAAQ,CAAC;IAY7B,IAAI,IAAI,OAAO,CAAC,GAAG,CAAC;IAK1B,QAAQ,IAAI,MAAM;IAIlB,MAAM,IAAI,MAAM;IAShB,KAAK,IAAI,aAAa;IAItB,OAAO,CAAC,QAAQ,CAQd;CACH"}